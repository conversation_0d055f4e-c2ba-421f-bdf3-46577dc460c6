export default {
  path: "/resource",
  redirect: "/resource/file-system",
  meta: {
    icon: "ic:outline-folder",
    // showLink: false,
    title: "资源管理",
    rank: 5
  },
  children: [
    {
      path: "/resource/file-system",
      meta: {
        title: "文件系统"
      },
      redirect: "/resource/file-system/storage-pool",
      children: [
        {
          path: "/resource/file-system/storage-pool",
          name: "StoragePool",
          component: () =>
            import("@/views/resource/file-system/storage-pool/index.vue"),
          meta: {
            title: "存储池"
          }
        },
        {
          path: "/resource/file-system/disk",
          name: "Disk",
          component: () =>
            import("@/views/resource/file-system/disk/index.vue"),
          meta: {
            title: "磁盘"
          }
        },
        {
          path: "/resource/file-system/file-sy",
          name: "FileSystem",
          component: () =>
            import("@/views/resource/file-system/file-sy/index.vue"),
          meta: {
            title: "文件系统"
          }
        },
        {
          path: "/resource/file-system/file-subtree",
          name: "FileSubtree",
          component: () =>
            import("@/views/resource/file-system/file-subtree/index.vue"),
          meta: {
            title: "文件子树"
          }
        },
        {
          path: "/resource/file-system/file-subtree-qos",
          name: "FileSubtreeQOS",
          component: () =>
            import("@/views/resource/file-system/file-subtree-qos/index.vue"),
          meta: {
            title: "文件子树QOS"
          }
        },
        /* 文件子树QOS的详情页 */
        {
          path: "/resource/file-system/file-subtree-qos/:name",
          name: "FileSubtreeQOSDetail",
          component: () =>
            import(
              "@/views/resource/file-system/file-subtree-qos/detail/index.vue"
            ),
          meta: {
            title: "文件子树QOS详情",
            showLink: false,
            activePath: "/resource/file-system/file-subtree-qos"
          }
        },
        {
          path: "/resource/file-system/file-subtree/:name",
          name: "FileSubtreeDetail",
          component: () =>
            import(
              "@/views/resource/file-system/file-subtree/detail/index.vue"
            ),
          meta: {
            title: "文件子树详情",
            showLink: false,
            activePath: "/resource/file-system/file-subtree"
          }
        },
        {
          path: "/resource/file-system/file-sy-permission",
          name: "FileSyPermission",
          component: () =>
            import("@/views/resource/file-system/file-sy-permission/index.vue"),
          meta: {
            title: "文件系统权限"
          }
        },
        {
          path: "/resource/file-system/file-sy-permission/detail/:name",
          name: "FileSyPermissionDetail",
          component: () =>
            import(
              "@/views/resource/file-system/file-sy-permission/detail/index.vue"
            ),
          meta: {
            title: "文件系统权限详情",
            showLink: false,
            activePath: "/resource/file-system/file-sy-permission"
          }
        }
      ]
    },
    {
      path: "/resource/protocol",
      meta: {
        title: "协议管理"
      },
      redirect: "/resource/protocol/nfs",
      children: [
        {
          path: "/resource/protocol/nfs",
          name: "NFS",
          component: () => import("@/views/resource/protocol/nfs/index.vue"),
          meta: {
            title: "NFS"
          }
        },
        {
          path: "/resource/protocol/nfs/detail/:name",
          name: "NfsDetail",
          component: () =>
            import("@/views/resource/protocol/nfs/detail/index.vue"),
          meta: {
            title: "NFS详情",
            showLink: false,
            activePath: "/resource/protocol/nfs"
          }
        },
        {
          path: "/resource/protocol/smb",
          name: "SMB",
          component: () => import("@/views/resource/protocol/smb/index.vue"),
          meta: {
            title: "SMB"
          }
        },
        {
          path: "/resource/protocol/s3",
          name: "S3",
          component: () => import("@/views/resource/protocol/s3/index.vue"),
          meta: {
            title: "S3"
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
