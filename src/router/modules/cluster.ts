export default {
  path: "/cluster",
  redirect: "/cluster/cluster",
  meta: {
    icon: "ic:outline-hub",
    // showLink: false,
    title: "集群管理",
    rank: 4
  },
  children: [
    {
      path: "/cluster/cluster",
      meta: {
        title: "集群"
      },
      redirect: "/cluster/cluster/local",
      children: [
        {
          path: "/cluster/cluster/local",
          name: "LocalClusterManagement",
          component: () => import("@/views/cluster/cluster/local/index.vue"),
          meta: {
            title: "本地集群管理",
            showParent: true
          }
        },
        {
          path: "/cluster/cluster/local/detail/:name",
          name: "ClusterLocalDetail",
          component: () =>
            import("@/views/cluster/cluster/local/detail/index.vue"),
          meta: {
            title: "本地集群详情",
            showLink: false,
            activePath: "/cluster/cluster/local"
          }
        }
      ]
    },
    {
      path: "/cluster/compute-node",
      meta: {
        title: "计算节点"
      },
      redirect: "/cluster/compute-node/virtual",
      children: [
        {
          path: "/cluster/compute-node/virtual",
          name: "Virtual",
          component: () =>
            import("@/views/cluster/compute-node/virtual/index.vue"),
          meta: {
            title: "虚拟组",
            showParent: true
          }
        },
        {
          path: "/cluster/compute-node/virtual/detail/:name",
          name: "VirtualDetail",
          component: () =>
            import("@/views/cluster/compute-node/virtual/detail/index.vue"),
          meta: {
            title: "虚拟组详情",
            showLink: false,
            activePath: "/cluster/compute-node/virtual"
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
