export default {
  path: "/permissions",
  meta: {
    icon: "ic:outline-lock",
    // showLink: false,
    title: "权限设置",
    rank: 7
  },
  children: [
    {
      path: "/permissions/platform-user",
      name: "PlatformUser",
      component: () => import("@/views/permissions/platform-user/index.vue"),
      meta: {
        title: "平台用户管理"
      }
    },
    {
      path: "/permissions/role-management",
      name: "RoleManagement",
      component: () => import("@/views/permissions/role-management/index.vue"),
      meta: {
        title: "角色管理"
      }
    },
    {
      path: "/permissions/security-policies",
      name: "SecurityPolicies",
      component: () =>
        import("@/views/permissions/security-policies/index.vue"),
      meta: {
        title: "安全策略"
      }
    }
  ]
} satisfies RouteConfigsTable;
