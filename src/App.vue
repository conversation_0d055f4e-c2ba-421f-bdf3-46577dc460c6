<template>
  <el-config-provider :locale="currentLocale">
    <router-view />
    <ReDialog />
  </el-config-provider>
</template>

<script lang="ts">
import { defineComponent, computed } from "vue";
import { ElConfigProvider } from "element-plus";
import { ReDialog } from "@/components/ReDialog";
import en from "element-plus/es/locale/lang/en";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { useGlobal } from "@pureadmin/utils";
import plusZhCn from "plus-pro-components/es/locale/lang/zh-cn";
import plusEn from "plus-pro-components/es/locale/lang/en";

export default defineComponent({
  name: "app",
  components: {
    [ElConfigProvider.name]: ElConfigProvider,
    ReDialog
  },
  setup() {
    const { $storage } = useGlobal<GlobalPropertiesApi>();

    const currentLocale = computed(() => {
      const locale = $storage.locale?.locale ?? "zh";
      return locale === "zh" ? { ...zhCn, ...plusZhCn } : { ...en, ...plusEn };
    });

    return {
      currentLocale
    };
  }
});
</script>

<style>
.el-dialog .plus-form-item__label,
.el-dialog .el-form-item__label {
  display: inline-flex;
  align-items: center;
}
</style>
