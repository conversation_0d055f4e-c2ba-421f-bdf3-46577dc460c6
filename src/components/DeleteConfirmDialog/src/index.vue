<script setup lang="ts">
import { ref, computed, watch } from "vue";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    required: true
  },
  itemName: {
    type: [String, Array] as PropType<string | string[]>,
    required: true
  }
});

const emit = defineEmits(["update:visible", "confirm"]);

const inputText = ref("");
const confirmationText = computed(() => `我已知晓相关风险，确认删除`);

// 判断“确认”按钮是否应该被禁用
const isConfirmDisabled = computed(
  () => inputText.value !== confirmationText.value
);

const displayItemName = computed(() => {
  let items: string[] = [];
  if (Array.isArray(props.itemName)) {
    items = props.itemName;
  } else if (typeof props.itemName === "string" && props.itemName.length > 0) {
    items = props.itemName.split(",").map(item => item.trim());
  }
  if (items.length === 0) {
    return "所选项";
  }
  if (items.length > 5) {
    return `${items.slice(0, 5).join(", ")} 等`;
  } else {
    return items.join(", ");
  }
});

const handleConfirm = () => {
  emit("confirm");
};

const handleClose = () => {
  emit("update:visible", false);
};

// 在弹窗关闭后清空输入框
watch(
  () => props.visible,
  newValue => {
    if (newValue === false) {
      setTimeout(() => {
        inputText.value = "";
      }, 200);
    }
  }
);

defineOptions({
  name: "DeleteConfirmDialog"
});
</script>

<template>
  <el-dialog
    :model-value="visible"
    :title="title"
    width="600px"
    @close="handleClose"
  >
    <div>
      <p class="mb-4">
        请确认您是否一定需要执行删除操作，执行删除操作可能导致部分数据丢失，请确认您已知晓并自愿承担执行删除操作所可能造成的一系列后果。
      </p>
      <p class="mb-4 text-red-500">
        您如果一定需要删除 <b class="text-black">{{ displayItemName }}</b
        >，请输入“<b>{{ confirmationText }}</b
        >”以确认删除。
      </p>
      <el-input v-model="inputText" :placeholder="confirmationText" />
    </div>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="danger"
        :disabled="isConfirmDisabled"
        @click="handleConfirm"
      >
        确认
      </el-button>
    </template>
  </el-dialog>
</template>

<style scoped>
.mb-4 {
  margin-bottom: 1rem;
}

.text-red-500 {
  color: #ef4444;
}
</style>
