<script setup lang="ts">
import { computed, type PropType } from "vue";
import IcOutlineSettings from "~icons/ic/outline-settings";

const props = defineProps({
  checkedColumnLabels: {
    type: Array as PropType<string[]>,
    default: () => []
  },
  columns: {
    type: Array as PropType<any[]>,
    default: () => []
  }
});

const emit = defineEmits(["update:checkedColumnLabels"]);

const localCheckedLabels = computed({
  get() {
    return props.checkedColumnLabels;
  },
  set(newValue) {
    emit("update:checkedColumnLabels", newValue);
  }
});

defineOptions({
  name: "ColumnSetting"
});
</script>

<template>
  <el-popover
    placement="bottom-end"
    title="列表显示设置"
    :width="200"
    trigger="click"
  >
    <template #reference>
      <el-button :icon="IcOutlineSettings" circle />
    </template>
    <div class="flex flex-col">
      <el-checkbox-group v-model="localCheckedLabels">
        <el-checkbox
          v-for="column in columns.filter(c => c.label)"
          :key="column.prop as any"
          :label="column.label"
          class="w-full"
        >
          {{ column.label }}
        </el-checkbox>
      </el-checkbox-group>
    </div>
  </el-popover>
</template>
