<script setup lang="ts">
defineProps({
  multipleSelection: {
    type: Array,
    default: () => []
  }
});

defineOptions({
  name: "TableToolbar"
});
</script>

<template>
  <!-- 无头组件，用于表格上方的响应式布局 -->
  <div class="flex flex-col md:flex-row md:justify-between gap-4">
    <div class="flex flex-col sm:flex-row sm:items-center gap-4">
      <div class="flex items-center gap-2">
        <slot name="search" />
      </div>

      <div class="flex items-center flex-wrap gap-0">
        <slot name="actions" />
      </div>

      <div
        v-show="multipleSelection.length > 0"
        class="flex items-center px-3 py-1 bg-blue-50 rounded-md border border-blue-100"
      >
        <span class="text-sm text-blue-600 font-medium">
          已选择 {{ multipleSelection.length }} 项
        </span>
      </div>
    </div>

    <div class="w-full md:w-auto flex justify-end">
      <slot name="settings" />
    </div>
  </div>
</template>
