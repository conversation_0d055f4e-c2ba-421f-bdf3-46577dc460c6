<script setup lang="ts">
defineProps({
  title: {
    type: String,
    default: "功能说明"
  },
  description: {
    type: String,
    required: true
  }
});

defineOptions({
  name: "InfoAlert"
});
</script>

<template>
  <div class="space-y-4">
    <el-alert :title="title" type="info" :closable="false" show-icon>
      <template #default>
        <div v-html="description" />
      </template>
    </el-alert>
  </div>
</template>
