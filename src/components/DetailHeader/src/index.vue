<script setup lang="ts">
import { type Component, type PropType } from "vue";
import IcOutlineFolderCopy from "~icons/ic/outline-folder-copy";

defineProps({
  name: {
    type: [String, Array],
    required: true
  },
  icon: {
    type: Object as PropType<Component>,
    default: () => IcOutlineFolderCopy
  }
});

defineOptions({
  name: "DetailHeader"
});
</script>

<template>
  <div
    class="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 pb-4 pl-5 pr-5"
  >
    <div class="flex items-center text-[var(--el-text-color-primary)]">
      <el-icon :size="32" class="mr-3" color="#0091EA">
        <component :is="icon" />
      </el-icon>
      <span class="text-xl-primary font-mono">{{ name }}</span>
      <div class="ml-2">
        <slot name="tags" />
      </div>
    </div>

    <div class="flex gap-2">
      <slot name="actions" />
    </div>
  </div>
</template>
