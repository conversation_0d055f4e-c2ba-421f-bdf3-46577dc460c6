import { http } from "@/utils/http";

export const getDiskList = (data?: object) => {
  return http.request<any>("get", "/resource/disk", data);
};

// 文件系统
export const getFileSystemList = (data?: object) => {
  return http.request<any>("get", "/resource/file-system", data);
};

// 文件子树
export const getFileSubtreeList = (data?: object) => {
  return http.request<any>("get", "/resource/file-subtree", data);
};

// 文件系统权限
export const getFileSystemPermissionList = (data?: object) => {
  return http.request<any>("get", "/resource/file-system-permission", data);
};
