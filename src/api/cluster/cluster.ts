import { http } from "@/utils/http";

// 这里定义返回值类型，使接口拥有良好的类型推导
export type LocalClusterResult = {
  /** 是否请求成功 */
  success: boolean;
  data: {
    list: any[];
    total: any;
  };
};

/** 获取本地集群列表接口，mock数据 */
export const getLocalClusterList = (data?: object) => {
  return http.request<LocalClusterResult>("get", "/cluster/local/list", data);
};

interface AddClusterRequest {
  clusterName: string;
  clusterType: string;
}
export const fetchAddCluster = (data: AddClusterRequest) => {
  return http.request<any>("post", "/cluster/local/add", { data });
};

/** 获取节点列表接口，mock数据 */
export const getNodeList = (data?: object) => {
  return http.request<LocalClusterResult>("get", "/cluster/node/list", data);
};
