import { useNav } from "./useNav";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { watch, onBeforeMount, type Ref } from "vue";

export function useTranslationLang(ref?: Ref) {
  const { $storage, changeTitle, handleResize } = useNav();
  const { locale, t } = useI18n();
  const route = useRoute();

  // 切换到中文
  function translationCh() {
    const newLocale = "zh";
    // 更新响应式存储
    $storage.locale = { locale: newLocale };
    // 更新 i18n 实例
    locale.value = newLocale;
    // 处理菜单重新渲染
    ref && handleResize(ref.value);
    // 更新页面标题
    changeTitle(route.meta);
  }

  // 切换到英文
  function translationEn() {
    const newLocale = "en";
    // 更新响应式存储
    $storage.locale = { locale: newLocale };
    // 更新 i18n 实例
    locale.value = newLocale;
    // 处理菜单重新渲染
    ref && handleResize(ref.value);
    // 更新页面标题
    changeTitle(route.meta);
  }

  // 监听语言变化，更新页面标题
  watch(
    () => locale.value,
    () => {
      changeTitle(route.meta);
    }
  );

  // 组件挂载前初始化语言设置
  onBeforeMount(() => {
    const storedLocale = $storage.locale?.locale ?? "zh";
    if (locale.value !== storedLocale) {
      locale.value = storedLocale;
    }
  });

  return {
    t,
    route,
    locale,
    translationCh,
    translationEn
  };
}
