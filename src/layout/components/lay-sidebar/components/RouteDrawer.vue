<script setup lang="ts">
import { computed, ref } from "vue";
import { useRouter } from "vue-router";
import { usePermissionStoreHook } from "@/store/modules/permission";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { toRaw } from "vue";
import { menuType } from "@/layout/types";
import IcOutlineSearch from "~icons/ic/outline-search";
// 搜索查询
const searchQuery = ref("");

interface Props {
  visible: boolean;
}

interface Emits {
  (e: "update:visible", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const router = useRouter();

// 获取完整的路由菜单数据
const wholeMenus = computed(() => usePermissionStoreHook().wholeMenus);

// 响应式抽屉尺寸
const drawerSize = computed(() => {
  if (typeof window === "undefined") return "40%";

  const width = window.innerWidth;
  if (width <= 599) return "95%";
  if (width <= 899) return "80%";
  if (width <= 1199) return "60%";
  if (width <= 1399) return "50%";
  return "45%";
});

// 处理抽屉关闭
function handleClose() {
  emit("update:visible", false);
}

// 处理路由跳转
function handleRouteClick(route: menuType) {
  if (route.path) {
    // 如果路由有重定向，使用重定向路径
    const targetPath = route.redirect || route.path;
    router.push(targetPath);
    handleClose();
  }
}

// 检测路由层级
function getRouteLevel(route: menuType): number {
  if (!route.children || route.children.length === 0) {
    return 1; // 一级路由（无子路由）
  }

  // 检查是否有三级路由
  const hasThirdLevel = route.children.some(
    child => child.children && child.children.length > 0
  );

  return hasThirdLevel ? 3 : 2;
}

// 渲染路由项
function renderRouteItem(route: menuType, isClickable: boolean = true) {
  return {
    route,
    isClickable,
    icon: toRaw(route.meta?.icon),
    title: route.meta?.title || route.name || route.path
  };
}

// 检查路由项是否匹配搜索查询
function isRouteMatchSearch(route: menuType, query: string): boolean {
  if (!query) return true;

  const searchLower = query.toLowerCase();
  const title = (route.meta?.title || route.name || route.path || "")
    .toString()
    .toLowerCase();

  return title.includes(searchLower);
}

// 处理路由数据分组
const routeGroups = computed(() => {
  const groups: Array<{
    dividerTitle: string;
    level: number;
    routes: Array<{
      route: menuType;
      isClickable: boolean;
      icon: any;
      title: string;
      children?: Array<{
        route: menuType;
        isClickable: boolean;
        icon: any;
        title: string;
      }>;
    }>;
  }> = [];

  wholeMenus.value.forEach(firstLevel => {
    const level = getRouteLevel(firstLevel);

    if (level === 1) {
      // 一级菜单路由：分割线 + 可点击项
      if (isRouteMatchSearch(firstLevel, searchQuery.value)) {
        groups.push({
          dividerTitle:
            firstLevel.meta?.title || firstLevel.name || firstLevel.path,
          level: 1,
          routes: [renderRouteItem(firstLevel, true)]
        });
      }
    } else if (level === 2) {
      // 二级菜单路由：分割线 + 二级可点击项
      const secondLevelRoutes =
        firstLevel.children
          ?.filter(child => isRouteMatchSearch(child, searchQuery.value))
          .map(child => renderRouteItem(child, true)) || [];

      if (
        secondLevelRoutes.length > 0 ||
        isRouteMatchSearch(firstLevel, searchQuery.value)
      ) {
        groups.push({
          dividerTitle:
            firstLevel.meta?.title || firstLevel.name || firstLevel.path,
          level: 2,
          routes: secondLevelRoutes
        });
      }
    } else if (level === 3) {
      // 三级菜单路由：分割线 + 二级分组标题 + 三级可点击项
      const secondLevelGroups =
        firstLevel.children
          ?.map(secondLevel => {
            const thirdLevelRoutes =
              secondLevel.children
                ?.filter(thirdLevel =>
                  isRouteMatchSearch(thirdLevel, searchQuery.value)
                )
                .map(thirdLevel => renderRouteItem(thirdLevel, true)) || [];

            if (
              thirdLevelRoutes.length > 0 ||
              isRouteMatchSearch(secondLevel, searchQuery.value)
            ) {
              return {
                ...renderRouteItem(secondLevel, false), // 二级标题不可点击
                children: thirdLevelRoutes
              };
            }
            return null;
          })
          .filter(Boolean) || [];

      if (
        secondLevelGroups.length > 0 ||
        isRouteMatchSearch(firstLevel, searchQuery.value)
      ) {
        groups.push({
          dividerTitle:
            firstLevel.meta?.title || firstLevel.name || firstLevel.path,
          level: 3,
          routes: secondLevelGroups
        });
      }
    }
  });

  return groups;
});
</script>

<template>
  <el-drawer
    :model-value="visible"
    title="搜索产品与服务"
    direction="ltr"
    :size="drawerSize"
    append-to-body
    :with-header="false"
    @close="handleClose"
  >
    <div class="route-drawer-content">
      <div class="search-container">
        <el-input
          v-model="searchQuery"
          placeholder="搜索产品与服务"
          clearable
          class="search-input"
          :prefix-icon="IcOutlineSearch"
        />
      </div>
      <!-- 空状态提示 -->
      <div v-if="routeGroups.length === 0" class="empty-search-result">
        <el-empty description="未找到匹配的产品或服务" />
      </div>

      <div
        v-for="(group, groupIndex) in routeGroups"
        :key="groupIndex"
        class="route-group"
      >
        <!-- 一级路由分割线标题 -->
        <el-divider content-position="left">
          <span class="divider-title">{{ group.dividerTitle }}</span>
        </el-divider>

        <!-- 一级菜单路由 -->
        <template v-if="group.level === 1">
          <div class="route-items">
            <div
              v-for="item in group.routes"
              :key="item.route.path"
              class="route-item clickable"
              @click="handleRouteClick(item.route)"
            >
              <div v-if="item.icon" class="route-icon">
                <component :is="useRenderIcon(item.icon)" />
              </div>
              <span class="route-title">{{ item.title }}</span>
            </div>
          </div>
        </template>

        <!-- 二级菜单路由 -->
        <template v-else-if="group.level === 2">
          <div class="route-items resource-grid">
            <div
              v-for="item in group.routes"
              :key="item.route.path"
              class="route-item resource-item clickable"
              @click="handleRouteClick(item.route)"
            >
              <div v-if="item.icon" class="route-icon">
                <component :is="useRenderIcon(item.icon)" />
              </div>
              <span class="route-title">{{ item.title }}</span>
            </div>
          </div>
        </template>

        <!-- 三级菜单路由 -->
        <template v-else-if="group.level === 3">
          <div class="resource-grid">
            <div
              v-for="secondLevel in group.routes"
              :key="secondLevel.route.path"
              class="resource-category"
            >
              <!-- 二级路由标题（不可点击） -->
              <div class="resource-category-title">
                <div v-if="secondLevel.icon" class="route-icon">
                  <component :is="useRenderIcon(secondLevel.icon)" />
                </div>
                <span class="title-text">{{ secondLevel.title }}</span>
              </div>

              <!-- 三级路由项 -->
              <div class="resource-items">
                <div
                  v-for="thirdLevel in secondLevel.children"
                  :key="thirdLevel.route.path"
                  class="resource-item clickable"
                  @click="handleRouteClick(thirdLevel.route)"
                >
                  <div v-if="thirdLevel.icon" class="route-icon">
                    <component :is="useRenderIcon(thirdLevel.icon)" />
                  </div>
                  <span class="route-title">{{ thirdLevel.title }}</span>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
  </el-drawer>
</template>

<style scoped>
@media (width >= 1400px) {
  .route-items,
  .resource-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (width <= 1399px) and (width >= 1200px) {
  .route-items,
  .resource-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (width <= 1199px) and (width >= 900px) {
  .route-items,
  .resource-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (width <= 899px) and (width >= 600px) {
  .route-items,
  .resource-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }

  .route-item {
    min-height: 42px !important;
    padding: 12px !important;
  }

  .route-title {
    font-size: 13px !important;
    font-weight: 600;
  }
}

@media (width <= 599px) {
  .route-items,
  .resource-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .route-item {
    min-height: 40px !important;
    padding: 10px 12px !important;
  }

  .route-title {
    font-size: 13px !important;
  }

  .route-icon {
    width: 16px !important;
    height: 16px !important;
    margin-right: 6px !important;
  }

  :deep(.el-divider) {
    margin-bottom: 20px !important;
  }
}

.route-drawer-content {
  padding: 0 16px 12px;
}

.search-container {
  width: 200px;
  padding: 0 0 16px;
  margin-bottom: 8px;
}

.search-input {
  width: 100%;
}

:deep(.search-input .el-input__wrapper) {
  border-radius: 4px;
  box-shadow: 0 0 0 1px var(--el-border-color) inset;
}

:deep(.search-input .el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

.empty-search-result {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
}

:deep(.el-drawer__header) {
  padding: 16px 16px 0;
  margin-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-drawer__title) {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.route-group {
  margin-bottom: 20px;
}

.route-group:last-child {
  padding-bottom: 0;
  margin-bottom: 0;
  border-bottom: none;
}

:deep(.el-divider) {
  margin-top: 0;
  margin-bottom: 20px;
}

:deep(.el-divider__text) {
  padding: 0 12px;
  background-color: var(--el-bg-color);
}

.divider-title {
  font-size: 14px;
  font-weight: 800;
  color: var(--el-text-color-primary);
}

.route-items {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 0;
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-top: 0;
}

.resource-category {
  overflow: hidden;
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.resource-category:hover {
  box-shadow: 0 2px 12px 0 rgb(0 0 0 / 5%);
}

.resource-category-title {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.resource-category-title .title-text {
  font-size: 13px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.resource-items {
  padding: 6px 8px;
}

.route-item {
  position: relative;
  display: flex;
  align-items: center;
  min-height: 40px;
  padding: 10px 12px;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 4px;
  transition: all 0.15s ease;
}

.resource-item {
  padding: 10px 12px;
  border-radius: 0;
}

.resource-item:last-child {
  border-bottom: none;
}

.route-item.clickable {
  cursor: pointer;
}

.route-item.clickable:hover {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.resource-item.clickable:hover {
  color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.route-icon {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  margin-right: 6px;
}

.route-title {
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 13px;
  font-weight: 400;
  line-height: 1.3;
  color: var(--el-text-color-regular);
  word-break: break-word;
  white-space: nowrap;
}

.route-item.clickable:hover .route-title {
  color: var(--el-color-primary);
}
</style>
