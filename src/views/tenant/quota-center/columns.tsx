import { getQuotaList } from "@/api/tenant/quota";
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";
import type { PlusColumn } from "plus-pro-components";
import type { Ref } from "vue";
import { onMounted, reactive, ref } from "vue";
import IcOutlineEdit from "~icons/ic/outline-edit";

export function useColumns({ onEdit }) {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const originalData = ref([]);

  const getList = async () => {
    loading.value = true;
    const { success, data } = await getQuotaList();
    if (success) {
      originalData.value = data.list;
      pagination.total = originalData.value.length;
    } else {
      message("获取配额列表失败", { type: "error" });
      originalData.value = [];
    }
    loading.value = false;
  };

  onMounted(async () => {
    await getList();
  });

  const checkedColumnLabels = ref([
    "模板ID",
    "模板名称",
    "模板类型",
    "文件子树",
    "被使用",
    "创建时间",
    "修改时间",
    "配额",
    "操作"
  ]);
  /* 
      templateID: "quota2",
    templateName: "gt-100G",
    templateType: "租户模板",
    fileSubtree: "fs0",
    used: "user01",
    createTime: "2025-08-06 10:00:00",
    updateTime: "2025-08-06 10:00:00",
    quota: "20G"
  */

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "模板ID",
      prop: "templateID",
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("模板ID")
    },
    {
      label: "模板名称",
      prop: "templateName",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("模板名称"),
      cellRenderer: ({ row }) => (
        <div class="flex items-center">
          <span class="mr-2">{row.templateName}</span>
          <el-icon
            class="cursor-pointer hover:text-primary"
            onClick={() => onEdit(row)}
          >
            <IcOutlineEdit />
          </el-icon>
        </div>
      )
    },
    {
      label: "模板类型",
      prop: "templateType",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("模板类型")
    },
    {
      label: "文件子树",
      prop: "fileSubtree",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("文件子树")
    },
    {
      label: "被使用",
      prop: "used",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("被使用")
    },
    {
      label: "创建时间",
      prop: "createTime",
      sortable: true,
      minWidth: 120,
      hide: () => !checkedColumnLabels.value.includes("创建时间")
    },
    {
      label: "修改时间",
      prop: "updateTime",
      sortable: true,
      minWidth: 120,
      hide: () => !checkedColumnLabels.value.includes("修改时间")
    },
    {
      label: "配额",
      prop: "quota",
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("配额"),
      cellRenderer: ({ row }) => (
        <div class="flex items-center">
          <span class="mr-2">{row.quota}</span>
          <el-icon
            class="cursor-pointer hover:text-primary"
            onClick={() => onEdit(row)}
          >
            <IcOutlineEdit />
          </el-icon>
        </div>
      )
    },
    {
      label: "操作",
      fixed: "right",
      slot: "operation",
      hide: () => !checkedColumnLabels.value.includes("操作")
    }
  ];
  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: 0,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = async () => {
    await getList();
    pagination.total = originalData.value.length;
  };

  const handleClick = (row: { templateID: any }) => {
    message(`查看配额模板ID: ${row.templateID}`, {
      showClose: true,
      type: "info"
    });
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  const getDialogFormColumns = (formModel: Ref<any>): PlusColumn[] => [
    {
      label: "模板名称",
      width: 120,
      prop: "templateName",
      valueType: "input"
    },
    {
      label: "模板类型",
      width: 120,
      prop: "templateType",
      valueType: "select",
      options: [
        {
          label: "租户模板",
          value: "tenant"
        },
        {
          label: "租户用户模板",
          value: "tenantUser"
        },
        {
          label: "租户用户组模板",
          value: "tenantUserGroup"
        }
      ]
    },
    {
      label: "配额",
      width: 120,
      prop: "quotaValue",
      tooltip: {
        content: `1 KiB = 1,024 Byte <br/>
        1 MiB = 1,024 KiB <br/>
        1 GiB = 1,024 MiB = 1,048,576 (10242) KiB <br/>
        1 TiB = 1,024 GiB = 1,073,741,824 (10243) KiB`,
        rawContent: true
      },
      renderField: () => {
        return (
          <div style={{ display: "flex", gap: "8px", width: "100%" }}>
            <el-input
              v-model={formModel.value.quotaValue}
              placeholder="请输入配额数值"
              style={{ flex: 1 }}
              type="number"
              min="1"
              max="1023"
              onInput={(val: any) => {
                const value = Number(val);
                if (value < 1) formModel.value.quotaValue = 1;
                if (value > 1023) formModel.value.quotaValue = 1023;
              }}
            />
            <el-select
              v-model={formModel.value.quotaUnit}
              style={{ width: "100px" }}
            >
              <el-option label="KiB" value="KiB" />
              <el-option label="MiB" value="MiB" />
              <el-option label="GiB" value="GiB" />
              <el-option label="TiB" value="TiB" />
            </el-select>
          </div>
        );
      }
    }
  ];

  const dialogFormRules = {
    templateName: [
      {
        required: true,
        message: "名称请以字母开头，可包含字母、数字、连字符(-)，长度最大15位",
        pattern: /^[a-zA-Z][a-zA-Z0-9-]{0,14}$/,
        trigger: "change"
      }
    ],
    templateType: [
      {
        required: true,
        message: "请选择模板类型",
        trigger: "blur"
      }
    ],
    quotaValue: [
      {
        required: true,
        message: "请输入配额数值",
        trigger: "change"
      }
    ]
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClick,
    handleClearSearch,
    getDialogFormColumns,
    dialogFormRules
  };
}
