<script setup lang="ts">
import { ColumnSetting } from "@/components/ColumnSetting";
import PageHeader from "@/components/PageHeader";
import IcOutlineInventory2 from "~icons/ic/outline-inventory-2";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { useColumns } from "./columns";
import { TableToolbar } from "@/components/TableToolbar";
import { PlusDialogForm } from "plus-pro-components";
import { message } from "@/utils/message";
import { computed, h, ref } from "vue";
import IcOutlineDelete from "~icons/ic/outline-delete";
import IcOutlinePlus from "~icons/ic/outline-plus";
import IcOutlineDeleteOutline from "~icons/ic/outline-delete-outline";
import { addDialog, closeDialog } from "@/components/ReDialog";

defineOptions({
  name: "QuotaCenter"
});

const parseQuota = (quotaStr: string) => {
  if (!quotaStr || typeof quotaStr !== "string") {
    return { quotaValue: "", quotaUnit: "GiB" };
  }
  const value = parseInt(quotaStr, 10);
  if (isNaN(value)) {
    return { quotaValue: quotaStr, quotaUnit: "GiB" };
  }
  const unitMap = {
    K: "KiB",
    M: "MiB",
    G: "GiB",
    T: "TiB",
    P: "PiB"
  };
  const unitMatch = quotaStr.match(/[a-zA-Z]+/);
  const unitStr = unitMatch ? unitMatch[0].toUpperCase() : "G";
  const finalUnit = unitMap[unitStr[0]] || "GiB";
  return {
    quotaValue: String(value),
    quotaUnit: finalUnit
  };
};

const formatQuota = (value: string, unit: string) => {
  if (!value) return "";
  return `${value}${unit}`;
};

const dialogTitle = computed(() =>
  isEdit.value ? "编辑配额模板" : "新增配额模板"
);

const dialogVisible = ref(false);
const dialogFormValues = ref<any>({});
const confirmLoading = ref(false);
const isEdit = ref(false);

const handleOpen = () => {
  isEdit.value = false;
  dialogFormValues.value = {
    templateName: "",
    templateType: "tenant",
    quotaValue: "",
    quotaUnit: "GiB"
  };
  dialogVisible.value = true;
};

const handleEditOpen = (row: any) => {
  isEdit.value = true;
  const { quotaValue, quotaUnit } = parseQuota(row.quota);
  dialogFormValues.value = {
    ...row,
    quotaValue,
    quotaUnit
  };
  dialogVisible.value = true;
};

const handleSubmit = async () => {
  confirmLoading.value = true;
  const payload = {
    ...dialogFormValues.value,
    quota: formatQuota(
      dialogFormValues.value.quotaValue,
      dialogFormValues.value.quotaUnit
    )
  };
  delete payload.quotaValue;
  delete payload.quotaUnit;
  console.log("正在提交的数据:", payload);
  if (isEdit.value) {
    message("修改成功", { type: "success" });
  } else {
    message("新增成功", { type: "success" });
  }

  confirmLoading.value = false;
  dialogVisible.value = false;
  handleRefreshTable();
};

const handleCancel = () => {
  dialogVisible.value = false;
};

function onCloseCallBackClick() {
  addDialog({
    title: "删除",
    width: 600,
    contentRenderer: () =>
      h(
        "p",
        "请确认您是否一定需要执行删除操作，执行删除操作可能导致使用该配额模板的用户或用户组的配额失效。"
      ),
    footerButtons: [
      {
        label: "取消",
        btnClick: ({ dialog: { options, index } }) => {
          console.log("取消删除");
          closeDialog(options, index);
        }
      },
      {
        label: "确认",
        type: "danger",
        btnClick: ({ dialog: { options, index } }) => {
          console.log("确认删除");
          closeDialog(options, index);
          handleRefreshTable();
        }
      }
    ]
  });
}

const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClick,
  handleClearSearch,
  pagination,
  getDialogFormColumns,
  dialogFormRules
} = useColumns({ onEdit: handleEditOpen });
const dialogColumns = computed(() => getDialogFormColumns(dialogFormValues));
</script>

<template>
  <div class="storage-pool-container">
    <el-card class="mb-4 shadow-sm">
      <PageHeader
        :icon="IcOutlineInventory2"
        title="配额中心"
        subtitle="Quota Center"
      />
    </el-card>
    <el-card class="shadow-sm">
      <template #header>
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 250px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>
          <template #actions>
            <el-button
              type="default"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
            <el-button type="primary" :icon="IcOutlinePlus" @click="handleOpen">
              新增
            </el-button>
            <el-button
              type="danger"
              :disabled="multipleSelection.length === 0"
              :icon="IcOutlineDeleteOutline"
              @click="onCloseCallBackClick()"
            >
              删除
            </el-button>
          </template>
          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </template>
      <pure-table
        ref="tableRef"
        :data="originalData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="ID"
        border
        @selection-change="handleSelectionChange"
      >
        <template #operation="{}">
          <el-button
            type="danger"
            plain
            size="small"
            :icon="IcOutlineDelete"
            @click="onCloseCallBackClick"
          />
        </template>
      </pure-table>
    </el-card>
    <PlusDialogForm
      v-model:visible="dialogVisible"
      v-model="dialogFormValues"
      :dialog="{
        title: dialogTitle,
        width: 600,
        confirmLoading,
        destroyOnClose: true
      }"
      :form="{
        columns: dialogColumns,
        rules: dialogFormRules,
        labelPosition: 'left',
        labelWidth: '150px',
        clearable: false
      }"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    />
  </div>
</template>
