import { reactive } from "vue";
import type { FormRules } from "element-plus";

/** 登录校验 */
export const loginRules = reactive<FormRules>({
  password: [
    {
      validator: (rule, value, callback) => {
        if (value === "") {
          callback(new Error("请输入密码"));
        } else if (value.length < 3 || value.length > 18) {
          callback(new Error("密码格式应为3-18位"));
        } else {
          callback();
        }
      },
      trigger: "blur"
    }
  ]
});
