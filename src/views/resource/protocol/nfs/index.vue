<script setup lang="ts">
import { ColumnSetting } from "@/components/ColumnSetting";
import InfoAlert from "@/components/InfoAlert";
import PageHeader from "@/components/PageHeader";
import { TableToolbar } from "@/components/TableToolbar";
import { message } from "@/utils/message";
import { type FieldValues, PlusDialogForm } from "plus-pro-components";
import { ref } from "vue";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineSettingsEthernet from "~icons/ic/outline-settings-ethernet";
import { useColumns } from "./columns";
import { DeleteConfirmDialog } from "@/components/DeleteConfirmDialog";

defineOptions({
  name: "NFS"
});
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination,
  fileSystem,
  selectFileSystem,
  dialogFormColumns,
  dialogFormRules
} = useColumns();

const dialogVisible = ref(false);
const dialogFormValues = ref<FieldValues>({});
const confirmLoading = ref(false);
const deleteDialogVisible = ref(false);

const handleOpen = () => {
  dialogVisible.value = true;
};

const handleSubmit = async () => {
  console.log(dialogFormValues.value);
  message("新增成功", {
    type: "success"
  });
};

const handleCancel = () => {
  dialogVisible.value = false;
  dialogFormValues.value = {};
  confirmLoading.value = false;
};

const onDeleteConfirm = async () => {
  if (!multipleSelection.value.length) return;
  const ids = multipleSelection.value.map((item: any) => item.clusterName);
  console.log("正在执行删除操作，目标:", ids);
  // await api.deleteCluster(itemToDelete.value.id);
  message(`NFS 协议 "${ids}" 删除成功`, {
    type: "success"
  });
  deleteDialogVisible.value = false;
  handleRefreshTable();
};
</script>

<template>
  <div class="storage-pool-container">
    <el-card class="mb-4 shadow-sm">
      <template #header>
        <PageHeader
          :icon="IcOutlineSettingsEthernet"
          title="NFS 协议"
          subtitle="版本号：V5.7-2025-08"
        />
      </template>
      <InfoAlert
        description="
        1. NFS描述。<br>
        2. NFS注意事项。<br>"
      />
    </el-card>

    <el-card class="shadow-sm">
      <template #header>
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <el-button type="primary" @click="handleOpen"> 新增 </el-button>
            <el-button
              type="danger"
              :disabled="multipleSelection.length === 0"
              @click="deleteDialogVisible = true"
            >
              删除
            </el-button>
            <div class="flex items-center ml-3">
              <el-select
                v-model="selectFileSystem"
                placeholder="选择文件系统"
                style="width: 150px"
              >
                <el-option
                  v-for="fs in fileSystem"
                  :key="fs.value"
                  :label="fs.label"
                  :value="fs.value"
                />
              </el-select>
            </div>
          </template>

          <template #settings>
            <el-button
              type="default"
              :icon="IcOutlineRefresh"
              :loading="loading"
              :circle="true"
              @click="handleRefreshTable"
            />
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </template>

      <pure-table
        ref="tableRef"
        :data="originalData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="ID"
        border
        table-layout="auto"
        @selection-change="handleSelectionChange"
      />
    </el-card>
    <PlusDialogForm
      v-model:visible="dialogVisible"
      v-model="dialogFormValues"
      :dialog="{
        title: '新增NFS',
        width: 600,
        closeOnPressEscape: true,
        closeOnClickModal: true,
        destroyOnClose: true,
        confirmLoading
      }"
      :form="{
        columns: dialogFormColumns,
        rules: dialogFormRules,
        labelPosition: 'left',
        labelWidth: '150px',
        clearable: true
      }"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    />
    <DeleteConfirmDialog
      v-model:visible="deleteDialogVisible"
      :title="`删除集群`"
      :item-name="
        multipleSelection.map((item: any) => item.shareName).join(', ')
      "
      @confirm="onDeleteConfirm"
    />
  </div>
</template>
