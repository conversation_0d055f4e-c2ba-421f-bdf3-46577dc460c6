<script setup lang="ts">
import { DetailHeader } from "@/components/DetailHeader";
import { ref, toRef } from "vue";
import IcOutlineFolder from "~icons/ic/outline-folder";
import { useDetail } from "../hooks";
import { message } from "@/utils/message";
import { DeleteConfirmDialog } from "@/components/DeleteConfirmDialog";
defineOptions({
  name: "NfsDetail"
});
const { initToDetail, getParameter, route, router } = useDetail();
const name = toRef(getParameter, "name");
initToDetail("params");

const detailData = ref({
  // 共享名称
  shareName: name.value,
  // 实际路径
  realPath: "/zion0/mstor/win10",
  // 读写权限
  permission: "只读",
  // 允许访问IP地址
  allowIp: "*",
  // 访问类型
  accessType: "NO_ROOT_SQUASH",
  // 创建时间
  createTime: "2021-07-20 10:00:00",
  // 委托信息
  delegation: "NONE",
  // 协议版本
  protocolVersion: "3,4",
  // 传输协议
  transportProtocol: "TCP",
  // 匿名用户UID
  anonymousUid: "TCP",
  // 匿名用户GID
  anonymousGid: "-2",
  // 安全类型
  securityType: "SYS",
  // 使用特权端口
  usePrivilegedPort: "FALSE",
  // 默认委托信息
  defaultDelegation: "NONE",
  // 客户端组标识符
  clientGroupId: "FALSE",
  // COMMIT请求
  commitRequest: "FALSE",
  // 伪路径
  pseudoPath: "/zion0/mstor/win10",
  // 备注
  remark: "123"
});

const goBack = () => {
  router.replace("/resource/protocol/nfs");
};

const deleteDialogVisible = ref(false);

const onDeleteConfirm = async () => {
  console.log("正在执行删除操作，目标:", detailData.value.shareName);
  // await api.deleteCluster(itemToDelete.value.id);
  message(`删除成功`, {
    type: "success"
  });
  deleteDialogVisible.value = false;
};
</script>

<template>
  <div>
    <el-card>
      <template #header>
        <el-page-header :title="null" @back="goBack" />
      </template>
      <DetailHeader :name="name" :icon="IcOutlineFolder">
        <template #actions>
          <el-button type="danger" @click="deleteDialogVisible = true"
            >删除</el-button
          >
        </template>
      </DetailHeader>
      <el-divider content-position="left" border-style="dashed"
        >基础信息</el-divider
      >
      <div class="info-grid-container">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">共享名称</span>
              <span class="info-value">{{ detailData.shareName }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">实际路径</span>
              <span class="info-value">{{ detailData.realPath }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">读写权限</span>
              <span class="info-value">{{ detailData.permission }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">允许访问IP地址</span>
              <span class="info-value">{{ detailData.allowIp }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">访问类型</span>
              <span class="info-value">{{ detailData.accessType }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">创建时间</span>
              <span class="info-value">{{ detailData.createTime }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">委托信息</span>
              <span class="info-value">{{ detailData.delegation }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">协议版本</span>
              <span class="info-value">{{ detailData.protocolVersion }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">传输协议</span>
              <span class="info-value">{{ detailData.transportProtocol }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">匿名用户UID</span>
              <span class="info-value">{{ detailData.anonymousUid }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">匿名用户GID</span>
              <span class="info-value">{{ detailData.anonymousGid }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">安全类型</span>
              <span class="info-value">{{ detailData.securityType }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">使用特权端口</span>
              <span class="info-value">{{ detailData.usePrivilegedPort }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">默认委托信息</span>
              <span class="info-value">{{ detailData.defaultDelegation }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">客户端组标识符</span>
              <span class="info-value">{{ detailData.clientGroupId }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">COMMIT请求</span>
              <span class="info-value">{{ detailData.commitRequest }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">伪路径</span>
              <span class="info-value">{{ detailData.pseudoPath }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">备注</span>
              <span class="info-value">{{ detailData.remark }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
    <DeleteConfirmDialog
      v-model:visible="deleteDialogVisible"
      :title="`删除`"
      :item-name="detailData.shareName"
      @confirm="onDeleteConfirm"
    />
  </div>
</template>

<style scoped>
.info-grid-container {
  padding: 0 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 18px;
}

.info-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.info-value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  word-break: break-all;
}

.custom-card-container {
  overflow: hidden;
  border-radius: 4px;
  transition: var(--el-transition-duration);
}

.toolbar {
  padding: 0 0 15px;
}
</style>
