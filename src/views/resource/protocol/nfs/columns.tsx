import { getNfsList } from "@/api/resource/protocol/nfs";
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";
import { onMounted, reactive, ref } from "vue";
import { useDetail } from "./hooks";
import type { PlusColumn } from "plus-pro-components";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const originalData = ref([]);
  const fileSystem = ref([
    {
      label: "fs0",
      value: 0
    },
    {
      label: "fs1",
      value: 1
    }
  ]);
  const selectFileSystem = ref(fileSystem.value[0].value);
  const { toDetail } = useDetail();

  const getList = async () => {
    loading.value = true;
    const { success, data } = await getNfsList();
    if (success) {
      originalData.value = data.list;
      pagination.total = originalData.value.length;
    } else {
      message("获取NFS列表失败", { type: "error" });
      originalData.value = [];
    }
    loading.value = false;
  };

  onMounted(async () => {
    await getList();
  });

  const checkedColumnLabels = ref([
    "共享名称",
    "实际路径",
    "读写权限",
    "允许访问ip地址",
    "访问类型",
    "创建时间",
    "备注"
  ]);

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 100
    },
    {
      label: "共享名称",
      prop: "shareName",
      width: 200,
      hide: () => !checkedColumnLabels.value.includes("共享名称"),
      cellRenderer: ({ row }) => {
        return (
          <div>
            <el-button type="primary" link onClick={() => handleToDetail(row)}>
              {row.shareName}
            </el-button>
          </div>
        );
      }
    },
    {
      label: "实际路径",
      prop: "actualPath",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("实际路径")
    },
    {
      label: "读写权限",
      prop: "permission",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("读写权限")
    },
    {
      label: "允许访问ip地址",
      prop: "allowedIP",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("允许访问ip地址")
    },
    {
      label: "访问类型",
      prop: "accessType",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("访问类型")
    },
    {
      label: "创建时间",
      prop: "createTime",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("创建时间")
    },
    {
      label: "备注",
      prop: "remark",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("备注")
    }
  ];

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: 0,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = async () => {
    await getList();
    pagination.total = originalData.value.length;
    message("刷新成功", { type: "success" });
  };

  const handleClick = (row: { shareName: any }) => {
    message(`查看共享名称: ${row.shareName}`, {
      showClose: true,
      type: "info"
    });
  };

  const handleToDetail = (row: { shareName: any }) => {
    toDetail({ name: row.shareName }, "params");
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  const dialogFormColumns: PlusColumn[] = [
    {
      label: "文件系统",
      width: 120,
      prop: "fileSystem",
      valueType: "select",
      options: [
        {
          label: "fs0",
          value: "fs0"
        },
        {
          label: "fs1",
          value: "fs1"
        }
      ]
    },
    {
      label: "文件子树",
      width: 120,
      prop: "fileSubtree",
      valueType: "select",
      options: [
        {
          label: "/zion0/mstor",
          value: "/zion0/mstor"
        }
      ]
    },
    {
      label: "共享名称",
      width: 120,
      prop: "shareName",
      valueType: "input",
      tooltip: {
        content: `NFS共享名称为共享目录实际路径下的最后一个文件夹名称。<br />
        如果输入"nfstest"，则NFS共享路径为"/zion*/子树名/nfstest"。`,
        rawContent: true
      }
    },
    {
      label: "读写权限",
      width: 120,
      prop: "readWritePermission",
      valueType: "select",
      options: [
        {
          label: "读写",
          value: "rw"
        },
        {
          label: "只读",
          value: "ro"
        }
      ]
    },
    {
      label: "访问身份",
      width: 120,
      prop: "accessIdentity",
      valueType: "select",
      tooltip: {
        content: `root_squash 将远程root用户的权限降低为匿名用户。<br />
        no_root_squash允许远程root用户保持root 权限访问NFS共享。`,
        rawContent: true
      },
      options: [
        {
          label: "no_root_squash",
          value: "no_root_squash"
        },
        {
          label: "root_squash",
          value: "root_squash"
        }
      ]
    },
    {
      label: "允许访问IP范围",
      width: 120,
      prop: "allowedIPRange",
      valueType: "input",
      tooltip: {
        content: `比如输入*为所有ip可以访问。<br />
        输入***********/24指定网段中的所有主机可以访问。`,
        rawContent: true
      }
    },
    {
      label: "备注",
      width: 120,
      prop: "remark",
      // 文本域
      valueType: "textarea"
    }
  ];

  const dialogFormRules = {
    shareName: [
      {
        required: true,
        message: "名称只能包含大小写字母和数字，且以字母开头，长度为2到15位",
        pattern: /^[a-zA-Z][a-zA-Z0-9]{1,14}$/,
        trigger: "change"
      }
    ],
    allowedIPRange: [
      {
        required: true,
        message: "请输入合法CIDR地址块",
        // pattern: /^(\d{1,3}\.){3}\d{1,3}\/\d{1,2}$/,
        trigger: "change"
      }
    ],
    remark: [
      {
        message: "请输入长度不超过255字符的内容",
        max: 255,
        trigger: "change"
      }
    ]
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClick,
    handleClearSearch,
    fileSystem,
    selectFileSystem,
    dialogFormColumns,
    dialogFormRules
  };
}
