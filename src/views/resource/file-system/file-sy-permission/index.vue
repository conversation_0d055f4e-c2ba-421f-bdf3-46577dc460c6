<script setup lang="ts">
import { useColumns } from "./columns";
import PageHeader from "@/components/PageHeader";
import InfoAlert from "@/components/InfoAlert";
import IcOutlineSdStorage from "~icons/ic/outline-sd-storage";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { ColumnSetting } from "@/components/ColumnSetting";
import { TableToolbar } from "@/components/TableToolbar";
import { ref } from "vue";

defineOptions({
  name: "FileSyPermission"
});
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination,
  fileSystem,
  selectFileSystem
} = useColumns();
const targetPath = ref("");
</script>

<template>
  <div class="storage-pool-container">
    <el-card class="mb-4 shadow-sm">
      <template #header>
        <PageHeader
          :icon="IcOutlineSdStorage"
          title="文件系统权限"
          subtitle="File System Permissions"
        />
      </template>
      <InfoAlert
        description="
        1. 文件系统权限描述。<br>
        2. 文件系统权限模块操作注意事项。<br>"
      />
    </el-card>

    <el-card class="shadow-sm">
      <template #header>
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <div class="flex items-center gap-4">
              <div class="flex items-center">
                <span>文件系统：</span>
                <el-select
                  v-model="selectFileSystem"
                  placeholder="选择文件系统"
                  style="width: 100px"
                >
                  <el-option
                    v-for="fs in fileSystem"
                    :key="fs.value"
                    :label="fs.label"
                    :value="fs.value"
                  />
                </el-select>
              </div>
              <div class="flex items-center">
                <span>根目录：</span>
                <span style="color: #777d80">/zion0</span>
              </div>
              <div class="flex items-center">
                <span>目标路径：</span>
                <el-input
                  v-model="targetPath"
                  placeholder="请输入目标路径"
                  style="width: 200px"
                  clearable
                />
              </div>
            </div>
          </template>

          <template #settings>
            <el-button
              type="default"
              :icon="IcOutlineRefresh"
              :loading="loading"
              :circle="true"
              @click="handleRefreshTable"
            />
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </template>

      <pure-table
        ref="tableRef"
        :data="originalData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="ID"
        border
        table-layout="auto"
        @selection-change="handleSelectionChange"
      />
    </el-card>
  </div>
</template>
