import { getFileSystemPermissionList } from "@/api/resource/file-sy";
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";
import { onMounted, reactive, ref } from "vue";
import { useDetail } from "./hooks";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const originalData = ref([]);
  const fileSystem = ref([
    {
      label: "fs0",
      value: 0
    },
    {
      label: "fs1",
      value: 1
    }
  ]);
  const selectFileSystem = ref(fileSystem.value[0].value);
  const { toDetail } = useDetail();

  const getList = async () => {
    loading.value = true;
    const { success, data } = await getFileSystemPermissionList();
    if (success) {
      originalData.value = data.list;
      pagination.total = originalData.value.length;
    } else {
      message("获取文件系统权限列表失败", { type: "error" });
      originalData.value = [];
    }
    loading.value = false;
  };

  onMounted(async () => {
    await getList();
  });

  const checkedColumnLabels = ref(["路径名称", "创建时间", "备注"]);

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 100
    },
    {
      label: "路径名称",
      prop: "pathName",
      sortable: true,
      width: 200,
      hide: () => !checkedColumnLabels.value.includes("路径名称"),
      cellRenderer: ({ row }) => {
        return (
          <div>
            <el-button type="primary" link onClick={() => handleToDetail(row)}>
              {row.pathName}
            </el-button>
          </div>
        );
      }
    },
    {
      label: "创建时间",
      prop: "createTime",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("创建时间")
    },
    {
      label: "备注",
      prop: "remark",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("备注")
    }
  ];

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: 0,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = async () => {
    await getList();
    pagination.total = originalData.value.length;
    message("刷新成功", { type: "success" });
  };

  const handleClick = (row: { pathName: any }) => {
    message(`查看文件系统权限: ${row.pathName}`, {
      showClose: true,
      type: "info"
    });
  };

  const handleToDetail = (row: { pathName: any }) => {
    toDetail({ name: row.pathName }, "params");
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClick,
    handleClearSearch,
    fileSystem,
    selectFileSystem
  };
}
