import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref } from "vue";
import { message } from "@/utils/message";
import { addDialog, closeDialog } from "@/components/ReDialog";
import { h } from "vue";
import type { FieldValues, PlusColumn } from "plus-pro-components";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();

  const checkedColumnLabels = ref([
    "类型",
    "用户组",
    "权限动行为",
    "权限",
    "是否继承",
    "操作"
  ]);

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "类型",
      prop: "type",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("类型")
    },
    {
      label: "用户组",
      prop: "userGroup",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("用户组")
    },
    {
      label: "权限动行为",
      prop: "permissionAction",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("权限动行为")
    },
    {
      label: "权限",
      prop: "permission",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("权限")
    },
    {
      label: "是否继承",
      prop: "inherit",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("是否继承"),
      cellRenderer: ({ row }) => <div>{row.inherit === 1 ? "是" : "否"}</div>
    },
    {
      label: "操作",
      fixed: "right",
      slot: "operation",
      hide: () => !checkedColumnLabels.value.includes("操作")
    }
  ];

  const originalData = ref([
    {
      ID: 4,
      type: "user",
      userGroup: "user",
      permissionAction: "allow",
      permission: "rwx",
      inherit: 1
    },
    {
      ID: 1,
      type: "group",
      userGroup: "group",
      permissionAction: "allow",
      permission: "rwx",
      inherit: 1
    },
    {
      ID: 2,
      type: "spl:group@",
      permissionAction: "allow",
      permission: "rx",
      inherit: 0
    },
    {
      ID: 3,
      type: "spl:everyone@",
      permissionAction: "allow",
      permission: "rx",
      inherit: 0
    }
  ]);

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: originalData.value.length,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = () => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      message("数据刷新成功", { type: "success" });
    }, 1000);
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  const handleDeleteAuth = () => {
    addDialog({
      title: "删除",
      width: 500,
      destroyOnClose: true,
      contentRenderer: () =>
        h(
          "p",
          "请确认您是否一定需要执行删除操作，执行删除操作可能导致数据安全风险，请谨慎操作。"
        ),
      footerButtons: [
        {
          label: "取消",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("取消");
            closeDialog(options, index);
          }
        },
        {
          label: "确认",
          type: "danger",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("确认");
            closeDialog(options, index);
          }
        }
      ]
    });
  };

  const userTypes = [
    {
      label: "user",
      value: "user"
    },
    {
      label: "group",
      value: "group"
    },
    {
      label: "spl:group@",
      value: "spl:group@"
    },
    {
      label: "spl:owner@",
      value: "spl:owner@"
    },
    {
      label: "spl:everyone@",
      value: "spl:everyone@"
    }
  ];

  const getAddAuthDialogFormColumns = (
    formValues: FieldValues
  ): PlusColumn[] => {
    const type = formValues.type as string;
    const isUserGroupHidden = !["user", "group"].includes(type);
    return [
      {
        label: "类型",
        prop: "type",
        valueType: "select",
        options: userTypes
      },
      {
        label: "用户组",
        prop: "userGroup",
        valueType: "select",
        tooltip: "仅在类型为user、group时显示",
        options: userTypes,
        hideInForm: isUserGroupHidden
      },
      {
        label: "权限动行为",
        prop: "permissionAction",
        valueType: "select",
        options: [
          {
            label: "允许",
            value: "allow"
          },
          {
            label: "禁止",
            value: "deny"
          }
        ]
      },
      {
        label: "权限",
        prop: "permission",
        valueType: "select",
        options: [
          {
            label: "rwx",
            value: "rwx"
          },
          {
            label: "rx",
            value: "rx"
          }
        ]
      },
      {
        label: "是否继承",
        prop: "inherit",
        valueType: "select",
        options: [
          {
            label: "是",
            value: 1
          },
          {
            label: "否",
            value: 0
          }
        ]
      }
    ];
  };

  const addAuthDialogFormRules = {
    fileSystem: [
      {
        required: true,
        message: "请选择文件系统",
        trigger: "blur"
      }
    ],
    fileSubtree: [
      {
        required: true,
        message: "请选择文件子树",
        trigger: "blur"
      }
    ],
    permission: [
      {
        required: true,
        message: "请选择可用权限",
        trigger: "blur"
      }
    ]
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClearSearch,
    handleDeleteAuth,
    getAddAuthDialogFormColumns,
    addAuthDialogFormRules
  };
}
