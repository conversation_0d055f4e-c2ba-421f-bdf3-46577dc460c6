import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref } from "vue";
import { message } from "@/utils/message";
import { uuid } from "@pureadmin/utils";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();

  const columns: TableColumnList = [
    {
      type: "expand",
      slot: "expand"
    },
    {
      label: "版本号",
      prop: "version",
      minWidth: 100,
      showOverflowTooltip: true
    },
    {
      label: "修改时间",
      prop: "updateTime",
      showOverflowTooltip: true
    },
    {
      label: "操作",
      fixed: "right",
      width: 200,
      slot: "operation"
    }
  ];

  const childColumns: TableColumnList = [
    {
      label: "类型",
      prop: "type"
    },
    {
      label: "用户组",
      prop: "userGroup"
    },
    {
      label: "权限动行为",
      prop: "permissionAction"
    },
    {
      label: "权限",
      prop: "permission"
    },
    {
      label: "是否继承",
      prop: "inherit",
      cellRenderer: (row: any) => {
        return row.inherit === 1 ? "是" : "否";
      }
    }
  ];

  const originalData = ref([
    {
      ID: uuid(),
      version: "ca5f437d-e21e-4a58-a0c7-1c468658a04b",
      updateTime: "2025-08-10 10:00:00",
      children: [
        {
          type: "spl:owner@",
          userGroup: "group1",
          permissionAction: "allow",
          permission: "rwx",
          inherit: 1
        },
        {
          type: "spl:owner@",
          userGroup: "group2",
          permissionAction: "allow",
          permission: "rwx",
          inherit: 1
        }
      ]
    },
    {
      ID: uuid(),
      version: "ca5f437d-e21e-4a58-a0c7-1c468658a04c",
      updateTime: "2025-08-11 10:00:00",
      children: [
        {
          type: "spl:owner@",
          userGroup: "group1",
          permissionAction: "allow",
          permission: "rwx",
          inherit: 1
        }
      ]
    },
    {
      ID: uuid(),
      version: "ca5f437d-e21e-4a58-a0c7-1c468658a04d",
      updateTime: "2025-08-12 10:00:00",
      children: [
        {
          type: "spl:owner@",
          userGroup: "group1",
          permissionAction: "allow",
          permission: "rwx",
          inherit: 1
        }
      ]
    }
  ]);

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: originalData.value.length,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = () => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      message("数据刷新成功", { type: "success" });
    }, 1000);
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    handleSelectionChange,
    handleRefreshTable,
    childColumns
  };
}
