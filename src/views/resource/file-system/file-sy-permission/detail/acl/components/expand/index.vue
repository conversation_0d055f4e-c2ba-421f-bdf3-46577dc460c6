<script setup lang="ts">
import { useColumns } from "./columns";

defineOptions({
  name: "AclExpand"
});
const {
  loading,
  tableRef,
  columns,
  originalData,
  handleSelectionChange,
  pagination,
  childColumns
} = useColumns();
</script>
<template>
  <div>
    <div class="custom-card-container">
      <div class="content-area">
        <pure-table
          ref="tableRef"
          :data="originalData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          row-key="ID"
          @selection-change="handleSelectionChange"
        >
          <template #expand="{ row }">
            <div class="pl-4 pr-4 pt-1 pb-1">
              <pure-table
                :data="row.children"
                :columns="childColumns"
                :border="true"
              />
            </div>
          </template>
          <template #operation="{}">
            <el-button type="primary" size="small" plain>回滚</el-button>
          </template>
        </pure-table>
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.toolbar {
  padding: 0 0 15px;
}
</style>
