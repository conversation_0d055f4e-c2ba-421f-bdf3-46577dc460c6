<script setup lang="ts">
import { ColumnSetting } from "@/components/ColumnSetting/index";
import { TableToolbar } from "@/components/TableToolbar/index";
import AclExpand from "./components/expand/index.vue";
import IcOutlineSearch from "~icons/ic/outline-search";
import { useColumns } from "./columns";
import { PlusDialog, PlusDialogForm } from "plus-pro-components";
import { computed, ref, watch } from "vue";
import { message } from "@/utils/message";

defineOptions({
  name: "AclInformation"
});
const addAuthDialogVisible = ref(false);
const addAuthDialogFormValues = ref<any>({});
const show = ref(false);
const dialogTitle = computed(() =>
  addAuthDialogFormValues.value.ID ? "编辑ACL" : "添加ACL"
);
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination,
  handleDeleteAuth,
  getAddAuthDialogFormColumns,
  addAuthDialogFormRules
} = useColumns();

const handleSubmit = async () => {
  console.log(addAuthDialogFormValues.value);
  message("添加成功", {
    type: "success"
  });
};

const handleCancel = () => {
  addAuthDialogVisible.value = false;
};

const handleReset = async () => {
  message("重置成功", {
    type: "success"
  });
  handleRefreshTable();
};

const handleConfirm = () => {
  message("删除成功", {
    type: "success"
  });
  show.value = false;
};

const handleOpen = () => {
  show.value = true;
};

const addAuthDialogFormColumns = computed(() => {
  return getAddAuthDialogFormColumns(addAuthDialogFormValues.value);
});

watch(
  () => addAuthDialogFormValues.value.type,
  newType => {
    if (!["user", "group"].includes(newType)) {
      addAuthDialogFormValues.value.userGroup = undefined;
    }
  }
);

const handleAddOrEdit = (row?: any) => {
  if (row) {
    addAuthDialogFormValues.value = { ...row };
  } else {
    addAuthDialogFormValues.value = {};
  }
  addAuthDialogVisible.value = true;
};
</script>
<template>
  <div>
    <div class="custom-card-container">
      <div class="toolbar">
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <!--             <el-button
              type="default"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button> -->
            <el-button type="primary" @click="handleAddOrEdit">
              添加ACL
            </el-button>
            <el-button type="primary" :loading="loading" @click="handleOpen">
              ACL操作记录
            </el-button>
            <el-popconfirm
              title="确认重置该路径下的ACL设置吗？"
              width="200px"
              placement="top"
              @confirm="handleReset"
            >
              <template #reference>
                <el-button type="warning" :disabled="!multipleSelection.length"
                  >重置</el-button
                >
              </template>
            </el-popconfirm>
          </template>

          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </div>

      <div class="content-area">
        <pure-table
          ref="tableRef"
          :data="originalData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          row-key="ID"
          border
          @selection-change="handleSelectionChange"
        >
          <template #operation="{ row }">
            <el-button
              type="primary"
              size="small"
              plain
              @click="handleAddOrEdit(row)"
              >编辑</el-button
            >
            <el-button
              type="danger"
              size="small"
              plain
              @click="handleDeleteAuth"
              >删除</el-button
            >
          </template>
        </pure-table>
      </div>
      <PlusDialogForm
        v-model:visible="addAuthDialogVisible"
        v-model="addAuthDialogFormValues"
        :dialog="{
          title: dialogTitle,
          width: 600,
          closeOnPressEscape: true,
          closeOnClickModal: true,
          destroyOnClose: true
        }"
        :form="{
          columns: addAuthDialogFormColumns,
          rules: addAuthDialogFormRules,
          labelPosition: 'left',
          labelWidth: '100px',
          clearable: true
        }"
        @confirm="handleSubmit"
        @cancel="handleCancel"
      />
      <PlusDialog
        v-model="show"
        title="ACL操作记录"
        cancel-text="取消"
        confirm-text="确定"
        :has-footer="false"
        :close-on-click-modal="true"
        width="1000px"
        @confirm="handleConfirm"
      >
        <AclExpand />
      </PlusDialog>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.toolbar {
  padding: 0 0 15px;
}
</style>
