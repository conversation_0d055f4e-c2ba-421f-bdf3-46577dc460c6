<script setup lang="ts">
import { DetailHeader } from "@/components/DetailHeader";
import { ref, toRef } from "vue";
import IcOutlineFolder from "~icons/ic/outline-folder";
import { useDetail } from "../hooks";
import AclInformation from "./acl/index.vue";

defineOptions({
  name: "FileSyPermissionDetail"
});
const { initToDetail, getParameter, route, router } = useDetail();
const name = toRef(getParameter, "name");
initToDetail("params");

const detailData = ref({
  // 路径名称
  pathName: name.value,
  // 创建时间
  createTime: "2025-03-20 10:00:00",
  // 备注
  remark: "测试文件子树"
});

const goBack = () => {
  router.replace("/resource/file-system/file-sy-permission");
};
</script>

<template>
  <div>
    <el-card>
      <template #header>
        <el-page-header :title="null" @back="goBack" />
      </template>
      <DetailHeader :name="name" :icon="IcOutlineFolder" />
      <el-divider content-position="left" border-style="dashed"
        >基础信息</el-divider
      >
      <div class="info-grid-container">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">路径名称</span>
              <span class="info-value">{{ detailData.pathName }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">创建时间</span>
              <span class="info-value">
                {{ detailData.createTime }}
              </span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">备注</span>
              <span class="info-value">{{ detailData.remark }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-tabs type="border-card" style="margin-top: 20px">
        <el-tab-pane label="ACL信息">
          <AclInformation />
        </el-tab-pane>
        <el-tab-pane label="子目录列表" />
      </el-tabs>
    </el-card>
  </div>
</template>

<style scoped>
.info-grid-container {
  padding: 0 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 18px;
}

.info-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.info-value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  word-break: break-all;
}

.custom-card-container {
  overflow: hidden;
  border-radius: 4px;
  transition: var(--el-transition-duration);
}

.toolbar {
  padding: 0 0 15px;
}
</style>
