import { reactive, ref } from "vue";
import type { PaginationProps } from "@pureadmin/table";
import { message } from "@/utils/message";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();

  const checkedColumnLabels = ref([
    "ID",
    "存储池名称",
    "存储池用途",
    "创建时间",
    "操作"
  ]);

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "ID",
      prop: "ID",
      hide: () => !checkedColumnLabels.value.includes("ID")
    },
    {
      label: "存储池名称",
      prop: "poolName",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("存储池名称")
    },
    {
      label: "存储池用途",
      prop: "poolType",
      slot: "poolType",
      hide: () => !checkedColumnLabels.value.includes("存储池用途")
    },
    {
      label: "创建时间",
      prop: "createTime",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("创建时间")
    },
    {
      label: "操作",
      fixed: "right",
      slot: "operation",
      hide: () => !checkedColumnLabels.value.includes("操作")
    }
  ];

  const originalData = ref([
    {
      ID: 1,
      poolName: "default-pool",
      poolType: "元数据存储池",
      createTime: "2025-07-21 10:00:00"
    },
    {
      ID: 2,
      poolName: "datapool-01",
      poolType: "数据存储池",
      createTime: "2025-07-22 10:00:00"
    },
    {
      ID: 3,
      poolName: "datapool-02",
      poolType: "数据存储池",
      createTime: "2025-07-23 10:00:00"
    }
  ]);

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: originalData.value.length,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = () => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      message("数据刷新成功", { type: "success" });
    }, 1000);
  };

  const handleClick = (row: { poolName: any }) => {
    message(`查看存储池: ${row.poolName}`, { type: "info" });
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClick,
    handleClearSearch
  };
}
