<script setup lang="ts">
import { useColumns } from "./columns";
import PageHeader from "@/components/PageHeader";
import InfoAlert from "@/components/InfoAlert";
import IcOutlineDns from "~icons/ic/outline-dns";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { ColumnSetting } from "@/components/ColumnSetting";
import { TableToolbar } from "@/components/TableToolbar";

defineOptions({
  name: "StoragePool"
});

const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClick,
  handleClearSearch,
  pagination
} = useColumns();
</script>

<template>
  <div class="storage-pool-container">
    <el-card class="mb-4 shadow-sm">
      <template #header>
        <PageHeader
          :icon="IcOutlineDns"
          title="存储池管理"
          subtitle="Storage Pool Management"
        />
      </template>
      <InfoAlert description="仅支持查看存储池信息，如需要操作，请联系管理员" />
    </el-card>

    <el-card class="shadow-sm">
      <template #header>
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <el-button
              type="primary"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
          </template>

          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </template>

      <pure-table
        ref="tableRef"
        :data="originalData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="ID"
        border
        @selection-change="handleSelectionChange"
      >
        <template #operation="{ row }">
          <el-button
            type="primary"
            size="small"
            plain
            @click="handleClick(row)"
          >
            查看详情
          </el-button>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>
