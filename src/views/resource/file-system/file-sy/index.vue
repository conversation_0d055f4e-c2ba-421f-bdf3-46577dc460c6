<script setup lang="ts">
import { useColumns } from "./columns";
import PageHeader from "@/components/PageHeader";
import InfoAlert from "@/components/InfoAlert";
import IcOutlineApartment from "~icons/ic/outline-apartment";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { ColumnSetting } from "@/components/ColumnSetting";
import { TableToolbar } from "@/components/TableToolbar";

defineOptions({
  name: "FileSystem"
});
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClick,
  handleClearSearch,
  pagination
} = useColumns();
</script>

<template>
  <div class="storage-pool-container">
    <el-card class="mb-4 shadow-sm">
      <template #header>
        <PageHeader
          :icon="IcOutlineApartment"
          title="文件系统"
          subtitle="FileSystems"
        />
      </template>
      <InfoAlert
        description="1. 仅支持查看文件系统信息，如需要操作，请联系管理员"
      />
    </el-card>

    <el-card class="shadow-sm">
      <template #header>
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <el-button
              type="primary"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
          </template>

          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </template>

      <pure-table
        ref="tableRef"
        :data="originalData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="ID"
        border
        @selection-change="handleSelectionChange"
      />
    </el-card>
  </div>
</template>
