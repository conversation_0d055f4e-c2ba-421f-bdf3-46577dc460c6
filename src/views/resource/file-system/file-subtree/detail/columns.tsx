import { reactive, ref } from "vue";
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";
import { bytesToSize } from "@/utils";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const checkedColumnLabels = ref([
    "名称",
    "状态",
    "路径",
    "块占用",
    "块配额",
    "文件数量",
    "已分配索引节点",
    "创建时间",
    "备注",
    "操作"
  ]);

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "名称",
      prop: "name",
      hide: () => !checkedColumnLabels.value.includes("名称")
    },
    {
      label: "状态",
      prop: "status",
      hide: () => !checkedColumnLabels.value.includes("状态"),
      cellRenderer: ({ row }) => {
        return (
          <el-tag type={row.status === "1" ? "success" : "info"}>
            {row.status === "1" ? "已挂载" : "未挂载"}
          </el-tag>
        );
      }
    },
    {
      label: "路径",
      prop: "path",
      hide: () => !checkedColumnLabels.value.includes("路径")
    },
    {
      label: "块占用",
      prop: "blockSize",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("块占用"),
      cellRenderer: ({ row }) => {
        return bytesToSize(row.blockSize);
      }
    },
    {
      label: "块配额",
      prop: "blockQuota",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("块配额")
    },
    {
      label: "文件数量",
      prop: "fileCount",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("文件数量")
    },
    {
      label: "已分配索引节点",
      prop: "indexNodeCount",
      showOverflowTooltip: true,
      minWidth: 120,
      hide: () => !checkedColumnLabels.value.includes("已分配索引节点")
    },
    {
      label: "创建时间",
      prop: "createTime",
      showOverflowTooltip: true,
      sortable: true,
      minWidth: 130,
      hide: () => !checkedColumnLabels.value.includes("创建时间")
    },
    {
      label: "备注",
      prop: "remark",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("备注")
    },
    {
      label: "操作",
      prop: "operation",
      hide: () => !checkedColumnLabels.value.includes("操作"),
      cellRenderer: ({}) => {
        return (
          <div>
            <el-button link type="primary">
              挂载子树
            </el-button>
            <el-button link type="danger">
              卸载子树
            </el-button>
          </div>
        );
      }
    }
  ];

  const originalData = ref([
    {
      ID: 1,
      name: "test",
      status: "1",
      path: "/test",
      blockSize: 1000000,
      blockQuota: 1000000000,
      fileCount: 10000,
      indexNodeCount: 5000,
      createTime: "2025-03-20 10:00:00",
      remark: "测试文件子树"
    },
    {
      ID: 2,
      name: "test1",
      status: "1",
      path: "/test1",
      blockSize: 1000000,
      blockQuota: 1000000000,
      fileCount: 10000,
      indexNodeCount: 5000,
      createTime: "2025-03-20 10:00:00",
      remark: "测试文件子树"
    }
  ]);

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: originalData.value.length,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = () => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      message("数据刷新成功", { type: "success" });
    }, 1000);
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClearSearch
  };
}
