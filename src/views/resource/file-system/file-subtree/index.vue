<script setup lang="ts">
import { useColumns } from "./columns";
import PageHeader from "@/components/PageHeader";
import InfoAlert from "@/components/InfoAlert";
import IcOutlineSdStorage from "~icons/ic/outline-sd-storage";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { ColumnSetting } from "@/components/ColumnSetting";
import { TableToolbar } from "@/components/TableToolbar";

defineOptions({
  name: "FileSubtree"
});
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination,
  fileSystem,
  selectFileSystem
} = useColumns();
</script>

<template>
  <div class="storage-pool-container">
    <el-card class="mb-4 shadow-sm">
      <template #header>
        <PageHeader
          :icon="IcOutlineSdStorage"
          title="文件子树"
          subtitle="File Subtrees"
        />
      </template>
      <InfoAlert
        description="
        1. 文件子树是一个特殊的目录，需要先创建再挂载才能使用。<br>
        2. 删除文件子树会删除整个目录的数据，请勿随意操作。<br>
        3. 文件子树配额只能增大不能减少。<br>
        4. 文件子树挂载路径必须要以/文件系统挂载路径/文件子树挂载路径"
      />
    </el-card>

    <el-card class="shadow-sm">
      <template #header>
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <el-button
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
            <el-button type="primary" @click="handleRefreshTable">
              新增
            </el-button>
            <el-button
              type="danger"
              :disabled="multipleSelection.length === 0"
              @click="handleRefreshTable"
            >
              删除
            </el-button>
            <div class="flex items-center ml-3">
              <el-select
                v-model="selectFileSystem"
                placeholder="选择文件系统"
                style="width: 150px"
              >
                <el-option
                  v-for="fs in fileSystem"
                  :key="fs.value"
                  :label="fs.label"
                  :value="fs.value"
                />
              </el-select>
            </div>
          </template>

          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </template>

      <pure-table
        ref="tableRef"
        :data="originalData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="ID"
        border
        table-layout="auto"
        @selection-change="handleSelectionChange"
      />
    </el-card>
  </div>
</template>
