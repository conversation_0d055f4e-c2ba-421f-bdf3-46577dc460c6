import { onMounted, reactive, ref } from "vue";
import type { PaginationProps } from "@pureadmin/table";
import { getFileSubtreeList } from "@/api/resource/file-sy";
import { message } from "@/utils/message";
import { bytesToSize } from "@/utils";
import { useDetail } from "./hooks";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const originalData = ref([]);
  const fileSystem = ref([
    {
      label: "fs0",
      value: 0
    },
    {
      label: "fs1",
      value: 1
    }
  ]);
  const selectFileSystem = ref(fileSystem.value[0].value);
  const { toDetail } = useDetail();

  const getList = async () => {
    loading.value = true;
    const { success, data } = await getFileSubtreeList();
    if (success) {
      originalData.value = data.list;
      pagination.total = originalData.value.length;
    } else {
      message("获取文件子树列表失败", { type: "error" });
      originalData.value = [];
    }
    loading.value = false;
  };

  onMounted(async () => {
    await getList();
  });

  const checkedColumnLabels = ref([
    "名称",
    "状态",
    "路径",
    "块占用",
    "块配额",
    "文件数量",
    "已分配索引节点",
    "创建时间",
    "备注",
    "操作"
  ]);

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "名称",
      prop: "name",
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("名称"),
      cellRenderer: ({ row }) => {
        return (
          <div>
            <el-button type="primary" link onClick={() => handleToDetail(row)}>
              {row.name}
            </el-button>
          </div>
        );
      }
    },
    {
      label: "状态",
      prop: "status",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("状态"),
      cellRenderer: ({ row }) => {
        return (
          <el-tag type={row.status === "1" ? "success" : "info"}>
            {row.status === "1" ? "已挂载" : "未挂载"}
          </el-tag>
        );
      }
    },
    {
      label: "路径",
      prop: "path",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("路径")
    },
    {
      label: "块占用",
      prop: "blockSize",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("块占用"),
      cellRenderer: ({ row }) => {
        return bytesToSize(row.blockSize);
      }
    },
    {
      label: "块配额",
      prop: "blockQuota",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("块配额")
    },
    {
      label: "文件数量",
      prop: "fileCount",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("文件数量")
    },
    {
      label: "已分配索引节点",
      prop: "indexNodeCount",
      showOverflowTooltip: true,
      sortable: true,
      minWidth: 120,
      hide: () => !checkedColumnLabels.value.includes("已分配索引节点")
    },
    {
      label: "创建时间",
      prop: "createTime",
      showOverflowTooltip: true,
      sortable: true,
      minWidth: 140,
      hide: () => !checkedColumnLabels.value.includes("创建时间")
    },
    {
      label: "备注",
      prop: "remark",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("备注")
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      width: 180,
      hide: () => !checkedColumnLabels.value.includes("操作"),
      cellRenderer: ({ row }) => {
        return (
          <div>
            <el-button
              link
              type="primary"
              onClick={() => {
                handleClick(row);
              }}
            >
              挂载子树
            </el-button>
            <el-button
              link
              type="danger"
              onClick={() => {
                handleUnMounted(row);
              }}
            >
              卸载子树
            </el-button>
          </div>
        );
      }
    }
  ];

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: 0,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = async () => {
    await getList();
    pagination.total = originalData.value.length;
    message("刷新成功", { type: "success" });
  };

  const handleClick = (row: { name: any }) => {
    message(`查看文件子树: ${row.name}`, { showClose: true, type: "info" });
  };

  const handleUnMounted = (row: { name: any }) => {
    message(`卸载子树: ${row.name}`, { type: "error" });
  };

  const handleToDetail = (row: { name: any }) => {
    toDetail({ name: row.name }, "params");
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClick,
    handleClearSearch,
    fileSystem,
    selectFileSystem
  };
}
