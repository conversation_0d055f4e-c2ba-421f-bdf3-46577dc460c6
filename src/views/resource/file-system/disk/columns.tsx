import { onMounted, reactive, ref } from "vue";
import type { PaginationProps } from "@pureadmin/table";
import { getDiskList } from "@/api/resource/file-sy";
import { ElProgress } from "element-plus";
import { message } from "@/utils/message";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const originalData = ref([]);

  const getList = async () => {
    loading.value = true;
    const { success, data } = await getDiskList();
    if (success) {
      originalData.value = data.list;
      pagination.total = originalData.value.length;
    } else {
      message("获取磁盘列表失败", { type: "error" });
      originalData.value = [];
    }
    loading.value = false;
  };

  onMounted(async () => {
    await getList();
  });

  const checkedColumnLabels = ref([
    "磁盘名称",
    "磁盘大小",
    "故障域",
    "碎片百分比",
    "可用百分比"
  ]);

  const columns: TableColumnList = [
    {
      label: "磁盘名称",
      prop: "diskName",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("磁盘名称")
    },
    {
      label: "磁盘大小",
      prop: "diskSize",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("磁盘大小")
    },
    {
      label: "故障域",
      prop: "faultDomain",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("故障域")
    },
    {
      label: "碎片百分比",
      prop: "fragmentPercent",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("碎片百分比"),
      cellRenderer: ({ row }) => {
        const percentage = Number(row.fragmentPercent) || 0;
        const getColor = (percentage: number) => {
          if (percentage >= 90) return "var(--el-color-danger)";
          if (percentage >= 70) return "var(--el-color-warning)";
          if (percentage >= 50) return "var(--el-color-primary)";
          return "var(--el-color-success)";
        };
        return (
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <ElProgress
              style={{ width: "100%" }}
              percentage={percentage}
              color={getColor(percentage)}
              striped={true}
              format={p => `${p}%`}
            />
          </div>
        );
      }
    },
    {
      label: "可用百分比",
      prop: "availablePercent",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("可用百分比"),
      cellRenderer: ({ row }) => {
        const percentage = Number(row.availablePercent) || 0;
        const getColor = (percentage: number) => {
          if (percentage <= 10) return "var(--el-color-danger)";
          if (percentage <= 30) return "var(--el-color-warning)";
          if (percentage <= 70) return "var(--el-color-primary)";
          return "var(--el-color-success)";
        };
        return (
          <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
            <ElProgress
              style={{ width: "250px" }}
              percentage={percentage}
              color={getColor(percentage)}
              striped={true}
              format={p => `${p}%`}
            />
          </div>
        );
      }
    }
  ];

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: 0,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = async () => {
    await getList();
    pagination.total = originalData.value.length;
  };

  const handleClick = (row: { diskName: any }) => {
    message(`查看磁盘: ${row.diskName}`, { type: "info" });
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClick,
    handleClearSearch
  };
}
