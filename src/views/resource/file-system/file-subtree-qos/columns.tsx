import { computed, reactive, ref } from "vue";
import type { PaginationProps } from "@pureadmin/table";
import { useDetail } from "./hooks";
import EditPen from "~icons/ep/edit-pen";
import Check from "~icons/ep/check";
import { message } from "@/utils/message";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const editMap = ref({});
  const activeIndex = ref(-1);
  const checkedColumnLabels = ref([
    "组名称",
    "组类型",
    "IOPS",
    "MBPS",
    "组内文件子树",
    "创建时间",
    "备注"
  ]);

  const { toDetail } = useDetail();
  const fileSystem = ref([
    {
      label: "fs0",
      value: 0
    },
    {
      label: "fs1",
      value: 1
    }
  ]);
  const selectFileSystem = ref(fileSystem.value[0].value);

  // 是否处于编辑状态
  const editing = computed(() => {
    return index => {
      // 检查编辑映射中是否存在该索引且其editing属性为true
      return editMap.value[index]?.editing;
    };
  });

  // 图标的CSS类名计算
  const iconClass = computed(() => {
    // index: 当前行的索引
    // other: 是否为其他图标（非编辑/保存图标）
    return (index, other = false) => {
      return [
        "cursor-pointer", // 鼠标悬停时显示手型光标
        "ml-2", // 左侧外边距
        "transition", // 添加过渡动画
        "delay-100", // 过渡延迟
        other
          ? ["hover:scale-110", "hover:text-red-500"] // 其他图标的悬停效果：放大1.1倍，颜色变为红色
          : editing.value(index) && ["scale-150", "text-red-500"] // 编辑/保存图标在编辑状态下的效果：放大1.5倍，颜色变为红色
      ];
    };
  });

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "组名称",
      prop: "groupName",
      width: 200,
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("组名称"),
      cellRenderer: ({ row }) => (
        <>
          <el-button type="primary" link onClick={() => handleClick(row)}>
            {row.groupName}
          </el-button>
        </>
      )
    },
    {
      label: "组类型",
      prop: "groupType",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("组类型")
    },
    {
      label: "IOPS",
      prop: "IOPS",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("IOPS"),
      // 配置可编辑模式
      cellRenderer: ({ row, index }) => (
        <div
          class="flex-bc w-full h-[32px]"
          onMouseenter={() => (activeIndex.value = index)}
          onMouseleave={() => onMouseleave(index)}
        >
          {!editing.value(index) ? (
            <p>{row.IOPS}</p>
          ) : (
            <>
              <el-input
                v-model={row.IOPS}
                type="number"
                min="1"
                onInput={(value: any) => {
                  if (Number(value) <= 0) {
                    // 如果输入小于等于0，则重置为1
                    row.IOPS = "1";
                  }
                }}
              />
              <iconify-icon-offline
                icon={Check}
                class={iconClass.value(index)}
                onClick={() => onSave(index)}
              />
            </>
          )}
          <iconify-icon-offline
            v-show={activeIndex.value === index && !editing.value(index)}
            icon={EditPen}
            class={iconClass.value(index, true)}
            onClick={() => onEdit(row, index)}
          />
        </div>
      )
    },
    {
      label: "MBPS",
      prop: "MBPS",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("MBPS")
    },
    {
      label: "组内文件子树",
      prop: "fileSubtree",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("组内文件子树"),
      cellRenderer: () => (
        <el-button type="primary" link>
          查看详情
        </el-button>
      )
    },
    {
      label: "创建时间",
      prop: "createTime",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("创建时间")
    },
    {
      label: "备注",
      prop: "notes",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("备注")
    }
  ];

  const originalData = ref([
    {
      ID: 1,
      groupName: "test",
      groupType: "user",
      IOPS: "100",
      MBPS: "10G",
      fileSubtree: 1,
      createTime: "2025-07-20 10:00:00",
      notes: "测试组"
    },
    {
      ID: 2,
      groupName: "dev_team_alpha",
      groupType: "developer",
      IOPS: "250",
      MBPS: "20G",
      fileSubtree: 1,
      createTime: "2025-07-25 11:30:00",
      notes: "开发团队A的工作组"
    },
    {
      ID: 3,
      groupName: "ops_prod",
      groupType: "operator",
      IOPS: "150",
      MBPS: "15G",
      fileSubtree: 1,
      createTime: "2025-07-28 14:15:00",
      notes: "生产运维专用组"
    },
    {
      ID: 4,
      groupName: "marketing_campaign",
      groupType: "marketing",
      IOPS: "80",
      MBPS: "8G",
      fileSubtree: 1,
      createTime: "2025-08-01 09:00:00",
      notes: "市场部宣传活动组"
    },
    {
      ID: 5,
      groupName: "qa_testing",
      groupType: "qa",
      IOPS: "200",
      MBPS: "18G",
      fileSubtree: 1,
      createTime: "2025-08-03 16:45:00",
      notes: "质量保证测试组"
    },
    {
      ID: 6,
      groupName: "infra_core",
      groupType: "infra",
      IOPS: "300",
      MBPS: "25G",
      fileSubtree: 1,
      createTime: "2025-08-04 09:10:00",
      notes: "核心基础设施管理组"
    }
  ]);

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: originalData.value.length,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = () => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      message("数据刷新成功", { type: "success" });
    }, 1000);
  };

  const handleClick = (row: { groupName: any }) => {
    toDetail({ name: row.groupName }, "params");
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  function onMouseleave(index) {
    editing.value[index]
      ? (activeIndex.value = index)
      : (activeIndex.value = -1);
  }

  function onEdit(row, index) {
    editMap.value[index] = Object.assign({ ...row, editing: true });
  }

  function onSave(index) {
    editMap.value[index].editing = false;
  }

  return {
    loading,
    searchValue,
    multipleSelection,
    fileSystem,
    selectFileSystem,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClick,
    handleClearSearch
  };
}
