<script setup lang="ts">
import { ColumnSetting } from "@/components/ColumnSetting";
import InfoAlert from "@/components/InfoAlert";
import PageHeader from "@/components/PageHeader";
import IcOutlineAccountTree from "~icons/ic/outline-account-tree";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { useColumns } from "./columns";
import { TableToolbar } from "@/components/TableToolbar";

defineOptions({
  name: "FileSubtreeQOS"
});
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClick,
  handleClearSearch,
  pagination,
  fileSystem,
  selectFileSystem
} = useColumns();
</script>

<template>
  <div class="storage-pool-container">
    <el-card class="mb-4 shadow-sm">
      <template #header>
        <PageHeader
          :icon="IcOutlineAccountTree"
          title="文件子树QOS"
          subtitle="FileSystem Subtree QOS"
        />
      </template>
      <InfoAlert
        description="1. 文件子树QOS描述。<br />2. 文件子树QOS注意事项。"
      />
    </el-card>
    <el-card class="shadow-sm">
      <template #header>
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 250px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <el-button
              type="default"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
            <el-button type="primary" @click="handleRefreshTable">
              新增
            </el-button>
            <el-button
              type="danger"
              :disabled="multipleSelection.length === 0"
              @click="handleRefreshTable"
            >
              删除
            </el-button>
            <div class="flex items-center ml-3">
              <el-select
                v-model="selectFileSystem"
                placeholder="选择文件系统"
                style="width: 150px"
              >
                <el-option
                  v-for="fs in fileSystem"
                  :key="fs.value"
                  :label="fs.label"
                  :value="fs.value"
                />
              </el-select>
            </div>
          </template>

          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </template>
      <pure-table
        ref="tableRef"
        :data="originalData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="ID"
        border
        @selection-change="handleSelectionChange"
      >
        <template #operation="{ row }">
          <el-button
            type="primary"
            size="small"
            plain
            @click="handleClick(row)"
          >
            查看详情
          </el-button>
        </template>
      </pure-table>
    </el-card>
  </div>
</template>
