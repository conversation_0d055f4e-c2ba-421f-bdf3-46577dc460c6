import { isString, isEmpty } from "@pureadmin/utils";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import {
  useRouter,
  useRoute,
  type LocationQueryRaw,
  type RouteParamsRaw
} from "vue-router";

export function useDetail() {
  const route = useRoute();
  const router = useRouter();
  const getParameter = isEmpty(route.params) ? route.query : route.params;

  function toDetail(
    parameter: LocationQueryRaw | RouteParamsRaw,
    model: "query" | "params"
  ) {
    Object.keys(parameter).forEach(param => {
      if (!isString(parameter[param])) {
        parameter[param] = parameter[param].toString();
      }
    });
    if (model === "query") {
      // 保存信息到标签页
      useMultiTagsStoreHook().handleTags("push", {
        path: `/resource/file-system/file-subtree-qos/detail`,
        name: "FileSubtreeQOSDetail",
        query: parameter,
        meta: {
          /* title: {
            zh: `文件子树QOS-详情信息`,
            en: `文件子树QOS-详情信息`
          }, */
          title: `文件子树QOS-详情信息`,
          dynamicLevel: 1
        }
      });
      // 路由跳转
      router.push({ name: "FileSubtreeQOSDetail", query: parameter });
    } else if (model === "params") {
      useMultiTagsStoreHook().handleTags("push", {
        path: `/resource/file-system/file-subtree-qos/detail/:name`,
        name: "FileSubtreeQOSDetail",
        params: parameter,
        meta: {
          title: `文件子树QOS-详情信息`
        }
      });
      router.push({ name: "FileSubtreeQOSDetail", params: parameter });
    }
  }

  const initToDetail = (model: "query" | "params") => {
    if (getParameter) toDetail(getParameter, model);
  };

  return { toDetail, initToDetail, getParameter, router, route };
}
