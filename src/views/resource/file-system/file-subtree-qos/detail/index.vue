<script setup lang="ts">
import { DetailHeader } from "@/components/DetailHeader";
import { ref, toRef } from "vue";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { useDetail } from "../hooks";
import { useColumns } from "./columns";
import { TableToolbar } from "@/components/TableToolbar";
import { ColumnSetting } from "@/components/ColumnSetting";

defineOptions({
  name: "FileSubtreeQOSDetail"
});
const { initToDetail, getParameter, route, router } = useDetail();
const name = toRef(getParameter, "name");
initToDetail("params");

const detailData = ref({
  name: name, // 从路由获取name
  groupType: "user",
  status: "已挂载",
  iops: 0,
  mbps: 0,
  createTime: "2021-07-20 10:00:00",
  notes: "123"
});

const goBack = () => {
  router.replace("/resource/file-system/file-subtree-qos");
};

const handleDelete = () => {
  // 处理删除逻辑
};

const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination
} = useColumns();
</script>

<template>
  <div>
    <el-card>
      <template #header>
        <el-page-header :title="null" @back="goBack" />
      </template>
      <!-- 左右对齐布局 -->
      <DetailHeader :name="name">
        <template #actions>
          <el-button type="danger" plain @click="handleDelete">删除</el-button>
        </template>
      </DetailHeader>
      <el-divider content-position="left" border-style="dashed"
        >基础信息</el-divider
      >
      <div class="info-grid-container">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">组名称</span>
              <span class="info-value">{{ detailData.name }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">组类型</span>
              <span class="info-value">
                {{ detailData.groupType }}&nbsp;
                <el-tag effect="dark" size="small" type="success">{{
                  detailData.status
                }}</el-tag>
              </span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">IOPS</span>
              <span class="info-value">{{ detailData.iops }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">MBPS</span>
              <span class="info-value">{{ detailData.mbps }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">创建时间</span>
              <span class="info-value">{{ detailData.createTime }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">备注</span>
              <span class="info-value">{{ detailData.notes }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <el-tabs type="border-card" style="margin-top: 20px">
        <el-tab-pane label="文件子树">
          <div class="custom-card-container">
            <div class="toolbar">
              <TableToolbar :multiple-selection="multipleSelection">
                <template #search>
                  <el-input
                    v-model="searchValue"
                    placeholder="按关键词搜索"
                    style="width: 250px"
                    clearable
                    @clear="handleClearSearch"
                  >
                    <template #prefix>
                      <el-icon>
                        <IcOutlineSearch />
                      </el-icon>
                    </template>
                  </el-input>
                </template>

                <template #actions>
                  <el-button
                    type="default"
                    :icon="IcOutlineRefresh"
                    :loading="loading"
                    @click="handleRefreshTable"
                  >
                    刷新
                  </el-button>
                  <el-button type="primary" @click="handleRefreshTable">
                    新增
                  </el-button>
                  <el-button
                    type="danger"
                    :disabled="multipleSelection.length === 0"
                    @click="handleRefreshTable"
                  >
                    删除
                  </el-button>
                </template>

                <template #settings>
                  <ColumnSetting
                    v-model:checkedColumnLabels="checkedColumnLabels"
                    :columns="columns"
                  />
                </template>
              </TableToolbar>
            </div>

            <div class="content-area">
              <pure-table
                ref="tableRef"
                :data="originalData"
                :columns="columns"
                :loading="loading"
                :pagination="pagination"
                row-key="ID"
                border
                @selection-change="handleSelectionChange"
              />
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="22222">222</el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style scoped>
.info-grid-container {
  padding: 0 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 18px;
}

.info-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.info-value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  word-break: break-all;
}

.custom-card-container {
  overflow: hidden; /* 防止内部元素溢出圆角 */
  border-radius: 4px;
  transition: var(--el-transition-duration);
}

.toolbar {
  padding: 0 0 15px;
}
</style>
