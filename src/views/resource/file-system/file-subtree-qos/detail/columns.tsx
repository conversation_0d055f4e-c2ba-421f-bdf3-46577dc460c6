import { reactive, ref } from "vue";
import type { PaginationProps } from "@pureadmin/table";
import { message } from "@/utils/message";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const checkedColumnLabels = ref(["文件子树名称", "添加时间", "备注"]);
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "文件子树名称",
      prop: "name",
      width: 200,
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("文件子树名称"),
      cellRenderer: ({ row }) => (
        <>
          <el-button type="primary" link>
            {row.name}
          </el-button>
        </>
      )
    },
    {
      label: "添加时间",
      prop: "createTime",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("添加时间")
    },
    {
      label: "备注",
      prop: "notes",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("备注")
    }
  ];

  const originalData = ref([
    {
      ID: 1,
      name: "test",
      createTime: "2025-07-20 10:00:00",
      notes: "测试文件子树"
    },
    {
      ID: 2,
      name: "test1",
      createTime: "2025-07-25 11:30:00",
      notes: "测试文件子树"
    },
    {
      ID: 3,
      name: "test2",
      createTime: "2025-07-25 11:30:11",
      notes: "测试文件子树"
    }
  ]);

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: originalData.value.length,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = () => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      message("数据刷新成功", { type: "success" });
    }, 1000);
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClearSearch
  };
}
