<script setup lang="ts">
import { ColumnSetting } from "@/components/ColumnSetting";
import PageHeader from "@/components/PageHeader";
import InfoAlert from "@/components/InfoAlert";
import IcOutlineDns from "~icons/ic/outline-dns";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import IcOutlinePlayCircleOutline from "~icons/ic/outline-play-circle-outline";
import IcOutlineStopCircle from "~icons/ic/outline-stop-circle";
import { useColumns } from "./columns";
import { TableToolbar } from "@/components/TableToolbar";

defineOptions({
  name: "Virtual"
});

const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination,
  handleStartOrStopGroup
} = useColumns();
</script>

<template>
  <div class="storage-pool-container">
    <el-card class="mb-4 shadow-sm">
      <template #header>
        <PageHeader
          :icon="IcOutlineDns"
          title="虚拟组"
          subtitle="Virtual Group"
        />
      </template>
      <InfoAlert
        description="1.一级界面展示虚拟组的基本信息：虚拟组名称、管理节点名称、管理节点IP、授权状态、租户以及创建时间；并可以启动或停止虚拟组。<br />2.二级界面分别为：节点状态、子树授权、QoS组授权 。"
      />
    </el-card>

    <el-card class="shadow-sm">
      <template #header>
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按虚拟组名称搜索"
              style="width: 300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <el-button
              type="primary"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
            <el-button
              type="primary"
              :icon="IcOutlinePlayCircleOutline"
              :disabled="!multipleSelection.length"
              @click="handleStartOrStopGroup(multipleSelection, 'start')"
            >
              启动
            </el-button>
            <el-button
              type="warning"
              :icon="IcOutlineStopCircle"
              :disabled="!multipleSelection.length"
              @click="handleStartOrStopGroup(multipleSelection, 'stop')"
            >
              停止
            </el-button>
          </template>

          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </template>
      <pure-table
        ref="tableRef"
        :data="originalData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="ID"
        border
        @selection-change="handleSelectionChange"
      />
    </el-card>
  </div>
</template>
