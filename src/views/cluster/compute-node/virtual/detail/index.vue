<script setup lang="ts">
import { DetailHeader } from "@/components/DetailHeader";
import { ref, toRef } from "vue";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { useDetail } from "../hooks";
import IcOutlineHub from "~icons/ic/outline-hub";
import NodeStatus from "./components/node/index.vue";
import FileSystemAuth from "./components/file-system/index.vue";
import QOSAuth from "./components/qos/index.vue";
defineOptions({
  name: "ClusterLocalDetail"
});
const { initToDetail, getParameter, route, router } = useDetail();
const name = toRef(getParameter, "name");
initToDetail("params");

const detailData = ref({
  // 虚拟组名称
  virtualGroupName: name,
  // 管理节点名称
  managerNodeName: "**********",
  // 管理节点IP
  managerNodeIP: "**********",
  // 授权状态
  status: 1,
  // 租户名称
  tenantName: "root",
  createTime: "2025-07-20 10:00:00",
  note: "test"
});

const goBack = () => {
  router.replace("/cluster/compute-node/virtual");
};

const handleDelete = () => {
  // 处理删除逻辑
};
</script>

<template>
  <div>
    <el-card>
      <template #header>
        <el-page-header :title="null" @back="goBack" />
      </template>
      <DetailHeader :name="name" :icon="IcOutlineHub">
        <template #actions>
          <el-button @click="handleDelete">启动虚拟组</el-button>
          <el-button @click="handleDelete">停止虚拟组</el-button>
        </template>
      </DetailHeader>
      <el-divider content-position="left" border-style="dashed"
        >基础信息</el-divider
      >
      <div class="info-grid-container">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :lg="6">
            <div class="info-item">
              <span class="info-label">虚拟组名称</span>
              <span class="info-value">{{ detailData.virtualGroupName }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="6">
            <div class="info-item">
              <span class="info-label">管理节点名称</span>
              <span class="info-value">
                {{ detailData.managerNodeName }}
              </span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="6">
            <div class="info-item">
              <span class="info-label">管理节点IP</span>
              <span class="info-value">{{ detailData.managerNodeIP }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="6">
            <div class="info-item">
              <span class="info-label">授权状态</span>
              <span class="info-value"
                ><el-tag :type="detailData.status === 1 ? 'success' : 'danger'">
                  {{ detailData.status === 1 ? "有效" : "无效" }}
                </el-tag></span
              >
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="6">
            <div class="info-item">
              <span class="info-label">租户名称</span>
              <span class="info-value">
                {{ detailData.tenantName }}
              </span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="6">
            <div class="info-item">
              <span class="info-label">创建时间</span>
              <span class="info-value">{{ detailData.createTime }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="6">
            <div class="info-item">
              <span class="info-label">备注</span>
              <span class="info-value">{{ detailData.note }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-tabs type="border-card" style="margin-top: 20px">
        <el-tab-pane label="节点状态">
          <NodeStatus />
        </el-tab-pane>
        <el-tab-pane label="文件子树授权">
          <FileSystemAuth />
        </el-tab-pane>
        <el-tab-pane label="QOS组授权">
          <QOSAuth />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style scoped>
.info-grid-container {
  padding: 0 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 18px;
}

.info-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.info-value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  word-break: break-all;
}

.custom-card-container {
  overflow: hidden; /* 防止内部元素溢出圆角 */
  border-radius: 4px;
  transition: var(--el-transition-duration);
}

.toolbar {
  padding: 0 0 15px;
}
</style>
