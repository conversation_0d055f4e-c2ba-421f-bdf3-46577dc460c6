import type { PaginationProps } from "@pureadmin/table";
import { reactive, ref } from "vue";
import { message } from "@/utils/message";
import { addDialog, closeDialog } from "@/components/ReDialog";
import { h } from "vue";
import type { PlusColumn } from "plus-pro-components";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const checkedColumnLabels = ref([
    "文件系统",
    "文件子树",
    "可用权限",
    "UID",
    "GID",
    "授权时间",
    "操作"
  ]);
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "文件系统",
      prop: "fileSystem",
      width: 200,
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("文件系统"),
      sortable: true
    },
    {
      label: "文件子树",
      prop: "fileSubtree",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("文件子树"),
      sortable: true
    },
    {
      label: "可用权限",
      prop: "availablePermissions",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("可用权限"),
      sortable: true
    },
    {
      label: "UID",
      prop: "uid",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("UID"),
      sortable: true
    },
    {
      label: "GID",
      prop: "gid",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("GID"),
      sortable: true
    },
    {
      label: "授权时间",
      prop: "authTime",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("授权时间"),
      sortable: true
    },
    {
      label: "操作",
      fixed: "right",
      width: 160,
      slot: "operation",
      hide: () => !checkedColumnLabels.value.includes("操作")
    }
  ];

  const originalData = ref([
    {
      ID: 1,
      fileSystem: "fs0",
      fileSubtree: "subtree0",
      availablePermissions: "只读",
      uid: "100001",
      gid: "100001",
      authTime: "2025-08-08 08:00:00",
      status: "1"
    },
    {
      ID: 2,
      fileSystem: "fs1",
      fileSubtree: "subtree1",
      availablePermissions: "读写",
      uid: "100002",
      gid: "100002",
      authTime: "2025-08-08 08:00:00",
      status: "1"
    },
    {
      ID: 3,
      fileSystem: "fs2",
      fileSubtree: "subtree2",
      availablePermissions: "读写",
      uid: "100003",
      gid: "100003",
      authTime: "2023-01-01 00:00:00",
      status: "1"
    },
    {
      ID: 4,
      fileSystem: "fs3",
      fileSubtree: "subtree3",
      availablePermissions: "读写",
      uid: "100004",
      gid: "100004",
      authTime: "2023-01-01 00:00:00",
      status: "1"
    },
    {
      ID: 5,
      fileSystem: "fs4",
      fileSubtree: "subtree4",
      availablePermissions: "读写",
      uid: "100005",
      gid: "100005",
      authTime: "2023-01-01 00:00:00",
      status: "1"
    }
  ]);

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: originalData.value.length,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = () => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      message("数据刷新成功", { type: "success" });
    }, 1000);
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  const handleCancelAuth = () => {
    addDialog({
      title: "取消授权",
      width: 500,
      destroyOnClose: true,
      contentRenderer: () =>
        h(
          "p",
          "请确认您是否一定需要执行取消授权操作，执行取消子树授权操作将使子树授权失效。"
        ),
      footerButtons: [
        {
          label: "取消",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("取消");
            closeDialog(options, index);
          }
        },
        {
          label: "确认",
          type: "danger",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("确认");
            closeDialog(options, index);
          }
        }
      ]
    });
  };

  const addAuthDialogFormColumns: PlusColumn[] = [
    {
      label: "文件系统",
      prop: "fileSystem",
      valueType: "select",
      options: [
        {
          label: "fs0",
          value: "fs0"
        },
        {
          label: "fs1",
          value: "fs1"
        }
      ]
    },
    {
      label: "文件子树",
      prop: "fileSubtree",
      valueType: "select",
      options: [
        {
          label: "subtree0",
          value: "subtree0"
        },
        {
          label: "subtree1",
          value: "subtree1"
        }
      ]
    },
    {
      label: "可用权限",
      prop: "permission",
      valueType: "select",
      options: [
        {
          label: "只读",
          value: "只读"
        },
        {
          label: "读写",
          value: "读写"
        }
      ]
    }
  ];

  const addAuthDialogFormRules = {
    fileSystem: [
      {
        required: true,
        message: "请选择文件系统",
        trigger: "blur"
      }
    ],
    fileSubtree: [
      {
        required: true,
        message: "请选择文件子树",
        trigger: "blur"
      }
    ],
    permission: [
      {
        required: true,
        message: "请选择可用权限",
        trigger: "blur"
      }
    ]
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClearSearch,
    handleCancelAuth,
    addAuthDialogFormColumns,
    addAuthDialogFormRules
  };
}
