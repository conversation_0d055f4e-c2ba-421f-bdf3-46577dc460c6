<script setup lang="ts">
import { ColumnSetting } from "@/components/ColumnSetting/index";
import { TableToolbar } from "@/components/TableToolbar/index";
import IcOutlineAddCircleOutline from "~icons/ic/outline-add-circle-outline";
import IcOutlineCancel from "~icons/ic/outline-cancel";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import IcOutlineSearch from "~icons/ic/outline-search";
import { useColumns } from "./columns";
import { PlusDialogForm } from "plus-pro-components";
import { ref } from "vue";
import { message } from "@/utils/message";
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination,
  handleCancelAuth,
  addAuthDialogFormColumns,
  addAuthDialogFormRules
} = useColumns();
defineOptions({
  name: "FileSystemAuth"
});
const addAuthDialogVisible = ref(false);
const addAuthDialogFormValues = ref({
  remark: ""
});

const handleSubmit = async () => {
  console.log(addAuthDialogFormValues.value);
  message("添加成功", {
    type: "success"
  });
};

const handleCancel = () => {
  addAuthDialogVisible.value = false;
};
</script>
<template>
  <div>
    <div class="custom-card-container">
      <div class="toolbar">
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <el-button
              type="default"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
            <el-button
              type="primary"
              :icon="IcOutlineAddCircleOutline"
              @click="addAuthDialogVisible = true"
            >
              添加授权
            </el-button>
            <el-button
              type="warning"
              :icon="IcOutlineCancel"
              :disabled="!multipleSelection.length"
              @click="handleCancelAuth"
            >
              取消授权
            </el-button>
          </template>

          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </div>

      <div class="content-area">
        <pure-table
          ref="tableRef"
          :data="originalData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          row-key="ID"
          border
          @selection-change="handleSelectionChange"
        >
          <template #operation="{}">
            <el-button
              type="danger"
              size="small"
              plain
              @click="handleCancelAuth"
              >取消授权</el-button
            >
          </template>
        </pure-table>
      </div>
      <PlusDialogForm
        v-model:visible="addAuthDialogVisible"
        v-model="addAuthDialogFormValues"
        :dialog="{
          title: '添加授权',
          width: 600,
          closeOnPressEscape: true,
          closeOnClickModal: true,
          destroyOnClose: true
        }"
        :form="{
          columns: addAuthDialogFormColumns,
          rules: addAuthDialogFormRules,
          labelPosition: 'left',
          labelWidth: '100px',
          clearable: true
        }"
        @confirm="handleSubmit"
        @cancel="handleCancel"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.toolbar {
  padding: 0 0 15px;
}
</style>
