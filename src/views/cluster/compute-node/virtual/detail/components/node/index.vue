<script setup lang="ts">
import { ColumnSetting } from "@/components/ColumnSetting/index";
import { TableToolbar } from "@/components/TableToolbar/index";
import IcOutlineAddCircleOutline from "~icons/ic/outline-add-circle-outline";
import IcOutlinePlayCircleOutline from "~icons/ic/outline-play-circle-outline";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import IcOutlineRemoveCircleOutline from "~icons/ic/outline-remove-circle-outline";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineStopCircle from "~icons/ic/outline-stop-circle";
import IcOutlineMoreHoriz from "~icons/ic/outline-more-horiz";
import { useColumns } from "./columns";
import { PlusDialogForm } from "plus-pro-components";
import { ref } from "vue";
import { message } from "@/utils/message";

defineOptions({
  name: "NodeStatus"
});
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination,
  handleStartOrStopNode,
  handleMountOrUnmountQosGroup,
  remarkDialogFormColumns,
  remarkDialogFormRules
} = useColumns();

const remarkDialogVisible = ref(false);
const remarkDialogFormValues = ref({
  remark: ""
});

const handleSubmit = async () => {
  console.log(remarkDialogFormValues.value);
  message("添加成功", {
    type: "success"
  });
};

const handleCancel = () => {
  remarkDialogVisible.value = false;
};
</script>
<template>
  <div>
    <div class="custom-card-container">
      <div class="toolbar">
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: l300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <el-button
              type="default"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
            <el-button
              type="primary"
              :icon="IcOutlinePlayCircleOutline"
              :disabled="multipleSelection.length === 0"
              @click="handleStartOrStopNode(multipleSelection, 'start')"
            >
              启动
            </el-button>
            <el-button
              type="warning"
              :icon="IcOutlineStopCircle"
              :disabled="!multipleSelection.length"
              @click="handleStartOrStopNode(multipleSelection, 'stop')"
            >
              停止
            </el-button>
            <el-button
              type="primary"
              :disabled="multipleSelection.length === 0"
              :icon="IcOutlineAddCircleOutline"
              @click="handleMountOrUnmountQosGroup(multipleSelection, 'start')"
            >
              挂载
            </el-button>
            <el-button
              type="warning"
              :disabled="multipleSelection.length === 0"
              :icon="IcOutlineRemoveCircleOutline"
              @click="handleMountOrUnmountQosGroup(multipleSelection, 'stop')"
            >
              卸载
            </el-button>
          </template>

          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </div>

      <div class="content-area">
        <pure-table
          ref="tableRef"
          :data="originalData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          row-key="ID"
          border
          @selection-change="handleSelectionChange"
        >
          <template #operation="{ row }">
            <div>
              <el-dropdown trigger="click">
                <el-button size="small">
                  <IcOutlineMoreHoriz class="w-5 h-5" />
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item
                      @click="handleStartOrStopNode([row], 'start')"
                    >
                      <span class="text-[#55ADFE]">启动节点</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      @click="handleStartOrStopNode([row], 'stop')"
                    >
                      <span class="text-red-500">停止节点</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      @click="handleMountOrUnmountQosGroup([row], 'start')"
                    >
                      <span class="text-[#55ADFE]">挂载QOS组</span>
                    </el-dropdown-item>
                    <el-dropdown-item
                      @click="handleMountOrUnmountQosGroup([row], 'stop')"
                    >
                      <span class="text-red-500">卸载QOS组</span>
                    </el-dropdown-item>
                    <el-dropdown-item @click="remarkDialogVisible = true">
                      <span class="text-[#55ADFE]">备注</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </pure-table>
      </div>
      <PlusDialogForm
        v-model:visible="remarkDialogVisible"
        v-model="remarkDialogFormValues"
        :dialog="{
          title: '节点状态备注',
          width: 600,
          closeOnPressEscape: true,
          closeOnClickModal: true,
          destroyOnClose: true
        }"
        :form="{
          columns: remarkDialogFormColumns,
          rules: remarkDialogFormRules,
          labelPosition: 'left',
          labelWidth: '100px',
          clearable: true
        }"
        @confirm="handleSubmit"
        @cancel="handleCancel"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.toolbar {
  padding: 0 0 15px;
}
</style>
