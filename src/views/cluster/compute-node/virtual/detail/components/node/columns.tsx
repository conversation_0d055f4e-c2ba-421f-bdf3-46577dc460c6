import { h, reactive, ref } from "vue";
import type { PaginationProps } from "@pureadmin/table";
import { message } from "@/utils/message";
import { addDialog, closeDialog } from "@/components/ReDialog";
import { getNames } from "@/utils";
import type { PlusColumn } from "plus-pro-components";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const checkedColumnLabels = ref([
    "节点名称",
    "IP地址",
    "节点状态",
    "存储服务状态",
    "已挂载QOS组",
    "备注",
    "操作"
  ]);
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "节点名称",
      prop: "nodeName",
      width: 200,
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("节点名称"),
      sortable: true
    },
    {
      label: "IP地址",
      prop: "ipAddress",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("IP地址"),
      sortable: true
    },
    {
      label: "节点状态",
      prop: "nodeStatus",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("节点状态"),
      sortable: true,
      cellRenderer(data: any) {
        if (data.nodeStatus === "0") {
          return <el-tag type="danger">离线</el-tag>;
        } else {
          return <el-tag type="success">在线</el-tag>;
        }
      }
    },
    {
      label: "存储服务状态",
      prop: "serverStatus",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("存储服务状态"),
      sortable: true,
      cellRenderer(data: any) {
        if (data.serverStatus === "0") {
          return <el-tag type="danger">停用</el-tag>;
        } else {
          return <el-tag type="success">启用</el-tag>;
        }
      }
    },
    {
      label: "已挂载QOS组",
      prop: "qosGroup",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("已挂载QOS组"),
      sortable: true,
      cellRenderer: ({ row }) =>
        row.qosGroup !== "" ? (
          <el-tag type="primary">{row.qosGroup}</el-tag>
        ) : (
          <el-tag type="info">未挂载</el-tag>
        )
    },
    {
      label: "备注",
      prop: "remark",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("备注"),
      sortable: true
    },
    {
      label: "操作",
      fixed: "right",
      width: 160,
      slot: "operation",
      hide: () => !checkedColumnLabels.value.includes("操作")
    }
  ];

  const originalData = [
    {
      ID: 1,
      nodeName: "test",
      ipAddress: "***********",
      nodeStatus: "1",
      serverStatus: "1",
      qosGroup: "test",
      remark: "123"
    },
    {
      ID: 2,
      nodeName: "test2",
      ipAddress: "***********",
      nodeStatus: "0",
      serverStatus: "0",
      qosGroup: "test2",
      remark: "123"
    },
    {
      ID: 3,
      nodeName: "test3",
      ipAddress: "***********",
      nodeStatus: "1",
      serverStatus: "1",
      qosGroup: "test3",
      remark: "123"
    },
    {
      ID: 4,
      nodeName: "test4",
      ipAddress: "***********",
      nodeStatus: "0",
      serverStatus: "0",
      qosGroup: "test4",
      remark: "123"
    },
    {
      ID: 5,
      nodeName: "test5",
      ipAddress: "***********",
      nodeStatus: "1",
      serverStatus: "1",
      qosGroup: "test5",
      remark: "123"
    }
  ];

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: originalData.length,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = () => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      message("数据刷新成功", { type: "success" });
    }, 1000);
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  const handleStartOrStopNode = (
    multipleSelection: any,
    option: "start" | "stop"
  ) => {
    const nodeNames = getNames(multipleSelection, "nodeName");
    addDialog({
      title: `${option === "start" ? "启动" : "停止"}节点`,
      width: 600,
      destroyOnClose: true,
      contentRenderer: () =>
        h(
          "p",
          multipleSelection.length
            ? `确定${option === "start" ? "启动" : "停止"}${nodeNames}节点吗？`
            : `确定${option === "start" ? "启动" : "停止"}选中的节点吗？`
        ),
      footerButtons: [
        {
          label: "取消",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("取消");
            closeDialog(options, index);
          }
        },
        {
          label: "确认",
          type: option === "start" ? "primary" : "danger",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("确认");
            closeDialog(options, index);
          }
        }
      ]
    });
  };

  const handleMountOrUnmountQosGroup = (
    multipleSelection: any,
    option: "start" | "stop"
  ) => {
    const nodeNames = getNames(multipleSelection, "nodeName");
    addDialog({
      title: `${option === "start" ? "挂载" : "卸载"}QOS组`,
      width: 600,
      destroyOnClose: true,
      contentRenderer: () =>
        h(
          "p",
          multipleSelection.length
            ? `确定${option === "start" ? "挂载" : "卸载"}${nodeNames}节点下的QoS组吗？`
            : `确定${option === "start" ? "挂载" : "卸载"}该节点下的QoS组吗？`
        ),
      footerButtons: [
        {
          label: "取消",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("取消");
            closeDialog(options, index);
          }
        },
        {
          label: "确认",
          type: option === "start" ? "primary" : "danger",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("确认");
            closeDialog(options, index);
          }
        }
      ]
    });
  };

  const remarkDialogFormColumns: PlusColumn[] = [
    {
      label: "备注",
      prop: "remark",
      valueType: "textarea"
    }
  ];

  const remarkDialogFormRules = {
    remark: [
      {
        required: true,
        message: "请输入长度不超过255字符的内容",
        pattern: /^.{0,255}$/,
        trigger: "blur"
      }
    ]
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClearSearch,
    handleStartOrStopNode,
    handleMountOrUnmountQosGroup,
    remarkDialogFormColumns,
    remarkDialogFormRules
  };
}
