<script setup lang="ts">
import { ColumnSetting } from "@/components/ColumnSetting/index";
import { TableToolbar } from "@/components/TableToolbar/index";
import IcOutlineAddCircleOutline from "~icons/ic/outline-add-circle-outline";
import IcOutlineCancel from "~icons/ic/outline-cancel";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import IcOutlineSearch from "~icons/ic/outline-search";
import { useColumns } from "./columns";
import { DeleteConfirmDialog } from "@/components/DeleteConfirmDialog";
import { computed, ref } from "vue";
import { message } from "@/utils/message";
import { PlusDialogForm } from "plus-pro-components";
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination,
  addQosDialogFormColumns,
  addQosDialogFormRules,
  handleMountOrUnmountQosGroup
} = useColumns({
  onSingleDelete: handleSingleDelete
});
defineOptions({
  name: "QOSAuth"
});

const deleteDialogVisible = ref(false);
const itemToDelete = ref(null);

const deleteItemName = computed(() => {
  if (multipleSelection.value.length > 0) {
    return multipleSelection.value.map((item: any) => item.qosGroup).join(", ");
  }
  if (itemToDelete.value) {
    return itemToDelete.value.qosGroup;
  }
  return "";
});

const onDeleteConfirm = async () => {
  message("删除成功", { type: "success" });
  deleteDialogVisible.value = false;
  itemToDelete.value = null;
};

function handleSingleDelete(row: any) {
  multipleSelection.value = [];
  itemToDelete.value = row;
  deleteDialogVisible.value = true;
}

function handleBatchDelete() {
  itemToDelete.value = null;
  deleteDialogVisible.value = true;
}

const qosAddDialogVisible = ref(false);
const qosAddDialogFormValues = ref({
  remark: ""
});

const handleSubmit = async () => {
  console.log(qosAddDialogFormValues.value);
  message("添加成功", {
    type: "success"
  });
};

const handleCancel = () => {
  qosAddDialogVisible.value = false;
};
</script>
<template>
  <div>
    <div class="custom-card-container">
      <div class="toolbar">
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <el-button
              type="default"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
            <el-button
              type="primary"
              :icon="IcOutlineAddCircleOutline"
              @click="qosAddDialogVisible = true"
            >
              授权
            </el-button>
            <el-button
              type="primary"
              :disabled="!multipleSelection.length"
              @click="handleMountOrUnmountQosGroup(multipleSelection, 'start')"
            >
              挂载
            </el-button>
            <el-button
              type="warning"
              :disabled="!multipleSelection.length"
              @click="handleMountOrUnmountQosGroup(multipleSelection, 'stop')"
            >
              取消挂载
            </el-button>
            <el-button
              type="danger"
              :disabled="!multipleSelection.length"
              @click="handleBatchDelete"
            >
              删除
            </el-button>
          </template>

          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </div>

      <div class="content-area">
        <pure-table
          ref="tableRef"
          :data="originalData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          row-key="ID"
          border
          @selection-change="handleSelectionChange"
        />
      </div>
      <PlusDialogForm
        v-model:visible="qosAddDialogVisible"
        v-model="qosAddDialogFormValues"
        :dialog="{
          title: '添加授权',
          width: 600,
          closeOnPressEscape: true,
          closeOnClickModal: true,
          destroyOnClose: true
        }"
        :form="{
          columns: addQosDialogFormColumns,
          rules: addQosDialogFormRules,
          labelPosition: 'left',
          labelWidth: '150px',
          clearable: true
        }"
        @confirm="handleSubmit"
        @cancel="handleCancel"
      />
      <DeleteConfirmDialog
        v-model:visible="deleteDialogVisible"
        :title="`删除QOS`"
        :item-name="deleteItemName"
        @confirm="onDeleteConfirm"
      />
    </div>
  </div>
</template>
<style lang="scss" scoped>
.toolbar {
  padding: 0 0 15px;
}
</style>
