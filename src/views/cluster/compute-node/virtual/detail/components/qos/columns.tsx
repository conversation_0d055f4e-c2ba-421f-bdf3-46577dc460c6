import { h, reactive, ref } from "vue";
import { dayjs } from "element-plus";
import type { PaginationProps } from "@pureadmin/table";
import IcOutlineMoreHoriz from "~icons/ic/outline-more-horiz";
import { message } from "@/utils/message";
import type { PlusColumn } from "plus-pro-components";
import { getNames } from "@/utils";
import { addDialog, closeDialog } from "@/components/ReDialog";

export function useColumns({
  onSingleDelete
}: {
  onSingleDelete: (row: any) => void;
}) {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const checkedColumnLabels = ref([
    "QOS组",
    "文件系统",
    "挂载路径",
    "是否挂载",
    "授权时间",
    "操作"
  ]);
  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "QOS组",
      prop: "qosGroup",
      width: 200,
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("QOS组"),
      sortable: true
    },
    {
      label: "文件系统",
      prop: "fileSystem",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("文件系统"),
      sortable: true
    },
    {
      label: "挂载路径",
      prop: "mountPath",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("挂载路径"),
      sortable: true
    },
    {
      label: "是否挂载",
      prop: "isMounted",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("是否挂载"),
      sortable: true,
      cellRenderer(data: any) {
        return data.isMounted === "1" ? (
          <el-tag type="success">已挂载</el-tag>
        ) : (
          <el-tag type="danger">未挂载</el-tag>
        );
      }
    },
    {
      label: "授权时间",
      prop: "authTime",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("授权时间"),
      sortable: true
    },
    {
      label: "操作",
      fixed: "right",
      width: 160,
      slot: "operation",
      hide: () => !checkedColumnLabels.value.includes("操作"),
      cellRenderer: ({ row }) => (
        <div>
          <el-dropdown trigger="click">
            {{
              default: () => (
                <el-button size="small">
                  <IcOutlineMoreHoriz class="w-5 h-5" />
                </el-button>
              ),
              dropdown: () => (
                <el-dropdown-menu>
                  <el-dropdown-item
                    onClick={() => {
                      handleMountOrUnmountQosGroup([row], "start");
                    }}
                  >
                    <span class="text-[#55ADFE]">挂载</span>
                  </el-dropdown-item>
                  <el-dropdown-item
                    onClick={() => handleMountOrUnmountQosGroup([row], "stop")}
                  >
                    <span class="text-[#55ADFE]">取消挂载</span>
                  </el-dropdown-item>
                  <el-dropdown-item onClick={() => onSingleDelete(row)}>
                    <span class="text-red-500">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              )
            }}
          </el-dropdown>
        </div>
      )
    }
  ];

  const originalData = ref([
    {
      ID: 1,
      qosGroup: "test",
      fileSystem: "fs0",
      mountPath: "/mnt/path1",
      isMounted: "1",
      authTime: dayjs().subtract(1, "day").format("YYYY-MM-DD HH:mm:ss")
    },
    {
      ID: 2,
      qosGroup: "test1",
      fileSystem: "fs1",
      mountPath: "/mnt/path2",
      isMounted: "0",
      authTime: dayjs().subtract(2, "day").format("YYYY-MM-DD HH:mm:ss")
    },
    {
      ID: 3,
      qosGroup: "test2",
      fileSystem: "fs2",
      mountPath: "/mnt/path3",
      isMounted: "1",
      authTime: dayjs().subtract(3, "day").format("YYYY-MM-DD HH:mm:ss")
    }
  ]);

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: originalData.value.length,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = () => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      message("数据刷新成功", { type: "success" });
    }, 1000);
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  const handleClick = (row: any) => {
    message(`操作 ${row.qosGroup} 成功`, { type: "success" });
  };

  const addQosDialogFormColumns: PlusColumn[] = [
    {
      label: "文件系统",
      prop: "fileSystem",
      valueType: "select",
      options: [
        {
          label: "fs0",
          value: "fs0"
        },
        {
          label: "fs1",
          value: "fs1"
        }
      ]
    },
    {
      label: "文件子树Qos组",
      prop: "fileQosGroup",
      valueType: "select",
      tooltip:
        "假如选择的qos组中的文件子树已被其他虚拟组授权，将自动过滤掉已选择过的文件子树。",
      options: [
        {
          label: "qos0",
          value: "qos0"
        },
        {
          label: "qos1",
          value: "qos1"
        }
      ]
    },
    {
      label: "文件子树",
      prop: "fileSubtree",
      valueType: "select",
      tooltip: {
        content: `当前选择框只接收所选的QOS组中的文件子树信息，请确保文件子树已挂载，<br/>挂载目录的所有者与所属组不为root，并且是已存在的用户，否则即使授权成功也会导致读写失败！`,
        rawContent: true
      },

      options: [
        {
          label: "subtree0",
          value: "subtree0"
        },
        {
          label: "subtree1",
          value: "subtree1"
        }
      ]
    },
    {
      label: "可用权限",
      prop: "permission",
      valueType: "select",
      options: [
        {
          label: "只读",
          value: "只读"
        },
        {
          label: "读写",
          value: "读写"
        }
      ]
    },
    {
      label: "挂在路径",
      prop: "mountPath",
      valueType: "input",
      tooltip: "该路径为绝对路径，例如：/vgrp0。"
    }
  ];

  const addQosDialogFormRules = {
    fileSystem: [
      {
        required: true,
        message: "请选择文件系统",
        trigger: "blur"
      }
    ],
    fileQosGroup: [
      {
        required: true,
        message: "请选择文件子树Qos组",
        trigger: "blur"
      }
    ],
    permission: [
      {
        required: true,
        message: "请选择可用权限",
        trigger: "blur"
      }
    ],
    mountPath: [
      {
        required: true,
        message: "请输入挂在路径",
        trigger: "blur"
      }
    ]
  };

  const handleMountOrUnmountQosGroup = (
    multipleSelection: any,
    option: "start" | "stop"
  ) => {
    const nodeNames = getNames(multipleSelection, "qosGroup");
    addDialog({
      title: `${option === "start" ? "挂载" : "取消挂载"}QOS组`,
      width: 600,
      destroyOnClose: true,
      contentRenderer: () =>
        h(
          "p",
          multipleSelection.length
            ? `确定${option === "start" ? "挂载" : "取消挂载"}${nodeNames} QoS组吗？`
            : `确定${option === "start" ? "挂载" : "取消挂载"}QoS组吗？`
        ),
      footerButtons: [
        {
          label: "取消",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("取消");
            closeDialog(options, index);
          }
        },
        {
          label: "确认",
          type: option === "start" ? "primary" : "danger",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("确认");
            closeDialog(options, index);
          }
        }
      ]
    });
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClearSearch,
    handleClick,
    addQosDialogFormColumns,
    addQosDialogFormRules,
    handleMountOrUnmountQosGroup
  };
}
