import { h, onMounted, reactive, ref } from "vue";
import { dayjs } from "element-plus";
import type { PaginationProps } from "@pureadmin/table";
import { getNodeList } from "@/api/cluster/cluster";
import { useDetail } from "./hooks";
import IcOutlineMoreHoriz from "~icons/ic/outline-more-horiz";
import { message } from "@/utils/message";
import { addDialog, closeDialog } from "@/components/ReDialog";
import { getNames } from "@/utils";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const originalData = ref([]);
  const { toDetail } = useDetail();

  const checkedColumnLabels = ref([
    "ID",
    "虚拟组名称",
    "管理节点名称",
    "管理节点IP",
    "授权状态",
    "租户",
    "创建时间",
    "操作"
  ]);

  const getList = async () => {
    loading.value = true;
    const { success, data } = await getNodeList();
    if (success) {
      originalData.value = data.list;
      pagination.total = originalData.value.length;
    } else {
      message("获取节点列表失败", { type: "error" });
      originalData.value = [];
    }
    loading.value = false;
  };

  onMounted(async () => {
    await getList();
  });

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: 0,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = async () => {
    await getList();
    pagination.total = originalData.value.length;
  };

  const handleToDetail = (row: any) => {
    console.log(row);
    toDetail({ name: row.groupName }, "params");
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  const handleClick = (row: any) => {
    console.log(row);
  };

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "虚拟组名称",
      prop: "groupName",
      showOverflowTooltip: true,
      minWidth: 130,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("虚拟组名称"),
      cellRenderer: ({ row }) => (
        <el-button type="primary" link onClick={() => handleToDetail(row)}>
          {row.groupName}
        </el-button>
      )
    },
    {
      label: "管理节点名称",
      prop: "nodeName",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("管理节点名称")
    },
    {
      label: "管理节点IP",
      prop: "nodeIp",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("管理节点IP"),
      cellRenderer: ({ row }) => <el-tag type="success">{row.nodeIp}</el-tag>
    },
    {
      label: "授权状态",
      prop: "status",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("授权状态"),
      cellRenderer: ({ row }) => (
        <el-tag type={row.status === "1" ? "success" : "danger"}>
          {row.status === "1" ? "已授权" : "未授权"}
        </el-tag>
      )
    },
    {
      label: "租户",
      prop: "tenant",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("租户")
    },
    {
      label: "创建时间",
      prop: "createTime",
      showOverflowTooltip: true,
      sortable: true,
      minWidth: 100,
      hide: () => !checkedColumnLabels.value.includes("创建时间"),
      cellRenderer: ({ row }) => (
        <>{dayjs(row.createTime).format("YYYY-MM-DD HH:mm:ss")}</>
      )
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      hide: () => !checkedColumnLabels.value.includes("操作"),
      slot: "operation",
      cellRenderer: ({ row }) => (
        <el-dropdown trigger="click">
          {{
            default: () => (
              <el-button size="small">
                <IcOutlineMoreHoriz class="w-5 h-5" />
              </el-button>
            ),
            dropdown: () => (
              <el-dropdown-menu>
                <el-dropdown-item
                  onClick={() => handleStartOrStopGroup([row], "start")}
                >
                  <span class="text-[#55ADFE]">启动虚拟组</span>
                </el-dropdown-item>
                <el-dropdown-item
                  onClick={() => handleStartOrStopGroup([row], "stop")}
                >
                  <span class="text-red-500">停止虚拟组</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            )
          }}
        </el-dropdown>
      )
    }
  ];

  const handleStartOrStopGroup = (
    multipleSelection: any,
    option: "start" | "stop"
  ) => {
    const names = getNames(multipleSelection, "groupName");
    addDialog({
      title: `${option === "start" ? "启动" : "停止"}虚拟组`,
      width: 600,
      destroyOnClose: true,
      contentRenderer: () =>
        h(
          "p",
          multipleSelection.length
            ? h("div", {
                innerHTML: `确定${
                  option === "start" ? "启动" : "停止"
                } <strong>${names}</strong> 虚拟组吗？`
              })
            : `确定${option === "start" ? "启动" : "停止"}选中的虚拟组吗？`
        ),
      footerButtons: [
        {
          label: "取消",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("取消");
            closeDialog(options, index);
          }
        },
        {
          label: "确认",
          type: option === "start" ? "primary" : "danger",
          btnClick: ({ dialog: { options, index } }) => {
            console.log("确认");
            closeDialog(options, index);
          }
        }
      ]
    });
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClick,
    handleToDetail,
    handleClearSearch,
    handleStartOrStopGroup
  };
}
