import { onMounted, reactive, ref } from "vue";
import { dayjs } from "element-plus";
import type { PaginationProps } from "@pureadmin/table";
import { getLocalClusterList } from "@/api/cluster/cluster";
import { useDetail } from "./hooks";
import IcOutlineMoreHoriz from "~icons/ic/outline-more-horiz";
import { message } from "@/utils/message";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const originalData = ref([]);
  const { toDetail } = useDetail();

  const checkedColumnLabels = ref([
    "ID",
    "集群名称",
    "集群类型",
    "集群管理节点",
    "最后心跳",
    "授权状态",
    "过期时间",
    "操作"
  ]);

  const getList = async () => {
    loading.value = true;
    const { success, data } = await getLocalClusterList();
    if (success) {
      originalData.value = data.list;
      pagination.total = originalData.value.length;
    } else {
      message("获取本地集群列表失败", { type: "error" });
      originalData.value = [];
    }
    loading.value = false;
  };

  onMounted(async () => {
    await getList();
  });

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: 0,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = async () => {
    await getList();
    pagination.total = originalData.value.length;
  };

  const handleToDetail = (row: { clusterName: any }) => {
    toDetail({ name: row.clusterName }, "params");
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  const handleClick = (row: any) => {
    console.log(row);
  };

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 50
    },
    {
      label: "集群名称",
      prop: "clusterName",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("集群名称"),
      cellRenderer: ({ row }) => (
        <el-button type="primary" link onClick={() => handleToDetail(row)}>
          {row.clusterName}
        </el-button>
      )
    },
    {
      label: "集群类型",
      prop: "clusterType",
      hide: () => !checkedColumnLabels.value.includes("集群类型")
    },
    {
      label: "集群管理节点",
      prop: "clusterMgrNodes",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("集群管理节点")
    },
    {
      label: "最后心跳",
      prop: "lastHeartbeat",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("最后心跳"),
      cellRenderer: ({ row }) => (
        <>{dayjs(row.lastHeartbeat).format("YYYY-MM-DD HH:mm:ss")}</>
      )
    },
    {
      label: "授权状态",
      prop: "authStatus",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("授权状态"),
      cellRenderer: ({ row }) => (
        <>
          <el-tag type={row.authStatus === 1 ? "success" : "danger"}>
            {row.authStatus === 1 ? "有效" : "无效"}
          </el-tag>
        </>
      )
    },
    {
      label: "过期时间",
      prop: "expireTime",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("过期时间"),
      cellRenderer: ({ row }) => (
        <>{dayjs(row.expireTime).format("YYYY-MM-DD HH:mm:ss")}</>
      )
    },
    {
      label: "操作",
      prop: "operation",
      fixed: "right",
      hide: () => !checkedColumnLabels.value.includes("操作"),
      slot: "operation",
      cellRenderer: ({ row }) => (
        <div>
          <el-dropdown trigger="click">
            {{
              default: () => (
                <el-button size="small">
                  <IcOutlineMoreHoriz class="w-5 h-5" />
                </el-button>
              ),
              dropdown: () => (
                <el-dropdown-menu>
                  <el-dropdown-item onClick={() => handleClick(row)}>
                    <span class="text-[#55ADFE]">下载集群凭证</span>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <span class="text-[#55ADFE]">添加集群凭证</span>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <span class="text-[#55ADFE]">重命名</span>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <span class="text-[#55ADFE]">添加管理节点</span>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <span class="text-red-500">删除管理节点</span>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <span class="text-red-500">删除集群</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              )
            }}
          </el-dropdown>
        </div>
      )
    }
  ];

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClick,
    handleClearSearch
  };
}
