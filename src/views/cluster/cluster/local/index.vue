<script setup lang="ts">
import { ColumnSetting } from "@/components/ColumnSetting";
import PageHeader from "@/components/PageHeader";
import {
  type FieldValues,
  type PlusColumn,
  PlusDialogForm
} from "plus-pro-components";
import { ref } from "vue";
import IcOutlineDns from "~icons/ic/outline-dns";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { useColumns } from "./columns";
import { message } from "@/utils/message";
import { fetchAddCluster } from "@/api/cluster/cluster";
import { DeleteConfirmDialog } from "@/components/DeleteConfirmDialog/index";
import { TableToolbar } from "@/components/TableToolbar/index";

defineOptions({
  name: "LocalClusterManagement"
});
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination
} = useColumns();

const dialogVisible = ref(false);
const dialogFormValues = ref<FieldValues>({});
const confirmLoading = ref(false);
const deleteDialogVisible = ref(false);

const handleOpen = () => {
  dialogVisible.value = true;
};

const dialogFormColumns: PlusColumn[] = [
  {
    label: "集群名称",
    width: 120,
    prop: "clusterName",
    valueType: "copy"
  },
  {
    label: "集群类型",
    width: 120,
    prop: "clusterType",
    valueType: "select",
    clearable: false,
    options: [
      {
        label: "nvfs",
        value: "nvfs"
      },
      {
        label: "nfs",
        value: "nfs"
      }
    ]
  }
];

const dialogFormRules = {
  clusterName: [{ required: true, message: "请输入集群名称" }],
  clusterType: [{ required: true, message: "请选择集群类型" }]
};

const addCluster = async () => {
  confirmLoading.value = true;
  try {
    const res = await fetchAddCluster({
      clusterName: dialogFormValues.value.clusterName as string,
      clusterType: dialogFormValues.value.clusterType as string
    });
    if (res.success) {
      message("新增集群成功", {
        type: "success"
      });
      dialogVisible.value = false;
      dialogFormValues.value = {};
      handleRefreshTable();
    } else {
      message(res.message, {
        type: "error"
      });
    }
  } finally {
    confirmLoading.value = false;
  }
};

const handleSubmit = async () => {
  await addCluster();
};

const handleCancel = () => {
  dialogVisible.value = false;
  dialogFormValues.value = {};
  confirmLoading.value = false;
};

const handleDeleteClick = () => {
  deleteDialogVisible.value = true;
};

const onDeleteConfirm = async () => {
  if (!multipleSelection.value.length) return;
  const ids = multipleSelection.value.map((item: any) => item.clusterName);
  console.log("正在执行删除操作，目标:", ids);
  // await api.deleteCluster(itemToDelete.value.id);
  message(`集群 "${ids}" 删除成功`, {
    type: "success"
  });
  deleteDialogVisible.value = false;
  handleRefreshTable();
};
</script>

<template>
  <div class="storage-pool-container">
    <el-card class="mb-4 shadow-sm">
      <PageHeader
        :icon="IcOutlineDns"
        title="本地集群管理"
        subtitle="Local cluster management"
      />
    </el-card>

    <el-card class="shadow-sm">
      <template #header>
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按集群名称和集群管理节点搜索"
              style="width: 300px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>

          <template #actions>
            <el-button
              type="primary"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
            <el-button type="primary" @click="handleOpen"> 新增 </el-button>
            <el-button
              type="danger"
              :disabled="multipleSelection.length === 0"
              @click="handleDeleteClick"
            >
              删除
            </el-button>
          </template>

          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </template>

      <pure-table
        ref="tableRef"
        :data="originalData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="ID"
        border
        @selection-change="handleSelectionChange"
      />
    </el-card>
    <PlusDialogForm
      v-model:visible="dialogVisible"
      v-model="dialogFormValues"
      :dialog="{
        title: '新增集群',
        width: 600,
        closeOnPressEscape: true,
        closeOnClickModal: true,
        destroyOnClose: true,
        confirmLoading
      }"
      :form="{
        columns: dialogFormColumns,
        rules: dialogFormRules,
        labelPosition: 'top',
        clearable: false
      }"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    />
    <DeleteConfirmDialog
      v-model:visible="deleteDialogVisible"
      :title="`删除集群`"
      :item-name="
        multipleSelection.map((item: any) => item.clusterName).join(', ')
      "
      @confirm="onDeleteConfirm"
    />
  </div>
</template>
