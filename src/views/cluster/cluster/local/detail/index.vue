<script setup lang="ts">
import { DetailHeader } from "@/components/DetailHeader";
import { ref, toRef } from "vue";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { useDetail } from "../hooks";
import { useColumns } from "./columns";
import IcOutlineHub from "~icons/ic/outline-hub";
import { TableToolbar } from "@/components/TableToolbar";
defineOptions({
  name: "ClusterLocalDetail"
});
const { initToDetail, getParameter, route, router } = useDetail();
const name = toRef(getParameter, "name");
initToDetail("params");

const detailData = ref({
  // 集群名称
  clusterName: name,
  // 集群类型
  clusterType: "nvfs",
  // 集群管理节点
  clusterManagerNodes: "**********",
  // 最后心跳
  lastHeartbeat: "2025-07-20 10:00:00",
  // 授权状态
  authorizationStatus: 1,
  // 过期时间
  expirationTime: "2025-07-20 10:00:00"
});

const goBack = () => {
  router.replace("/cluster/cluster/local");
};

const handleDelete = () => {
  // 处理删除逻辑
};

const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClearSearch,
  pagination
} = useColumns();
</script>

<template>
  <div>
    <el-card>
      <template #header>
        <el-page-header :title="null" @back="goBack" />
      </template>
      <!-- 左右对齐布局 -->
      <DetailHeader :name="name" :icon="IcOutlineHub">
        <template #actions>
          <el-button-group>
            <el-button>下载集群凭证</el-button>
            <el-button>添加集群凭证</el-button>
            <el-button>重命名</el-button>
            <el-button>删除管理节点</el-button>
            <el-button>删除集群</el-button>
          </el-button-group>
          <el-button type="primary" @click="handleDelete"
            >添加管理节点</el-button
          >
        </template>
      </DetailHeader>
      <el-divider content-position="left" border-style="dashed"
        >基础信息</el-divider
      >
      <div class="info-grid-container">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">集群名称</span>
              <span class="info-value">{{ detailData.clusterName }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">集群类型</span>
              <span class="info-value">
                {{ detailData.clusterType }}
              </span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">集群管理节点</span>
              <span class="info-value">{{
                detailData.clusterManagerNodes
              }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">最后心跳</span>
              <span class="info-value">{{ detailData.lastHeartbeat }}</span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">授权状态</span>
              <span class="info-value">
                <el-tag
                  :type="
                    detailData.authorizationStatus === 1 ? 'success' : 'danger'
                  "
                >
                  {{
                    detailData.authorizationStatus === 1 ? "已授权" : "未授权"
                  }}
                </el-tag>
              </span>
            </div>
          </el-col>
          <el-col :xs="24" :sm="12" :lg="8">
            <div class="info-item">
              <span class="info-label">过期时间</span>
              <span class="info-value">{{ detailData.expirationTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-tabs type="border-card" style="margin-top: 20px">
        <el-tab-pane label="本地集群">
          <div class="custom-card-container">
            <div class="toolbar">
              <TableToolbar :multiple-selection="multipleSelection">
                <template #search>
                  <el-input
                    v-model="searchValue"
                    placeholder="根据节点名称和IP地址搜索"
                    style="width: 250px"
                    clearable
                    @clear="handleClearSearch"
                  >
                    <template #prefix>
                      <el-icon>
                        <IcOutlineSearch />
                      </el-icon>
                    </template>
                  </el-input>
                </template>
                <template #actions>
                  <el-button
                    type="default"
                    :icon="IcOutlineRefresh"
                    :loading="loading"
                    @click="handleRefreshTable"
                  >
                    刷新
                  </el-button>
                  <el-button type="primary" @click="handleRefreshTable">
                    新增
                  </el-button>
                  <el-button
                    type="danger"
                    :disabled="multipleSelection.length === 0"
                    @click="handleRefreshTable"
                  >
                    删除
                  </el-button>
                </template>
                <template #settings>
                  <ColumnSetting
                    v-model:checkedColumnLabels="checkedColumnLabels"
                    :columns="columns"
                  />
                </template>
              </TableToolbar>
            </div>

            <div class="content-area">
              <pure-table
                ref="tableRef"
                :data="originalData"
                :columns="columns"
                :loading="loading"
                :pagination="pagination"
                row-key="ID"
                border
                @selection-change="handleSelectionChange"
              />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<style scoped>
.info-grid-container {
  padding: 0 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 18px;
}

.info-label {
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--el-text-color-secondary);
}

.info-value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  word-break: break-all;
}

.custom-card-container {
  overflow: hidden; /* 防止内部元素溢出圆角 */
  border-radius: 4px;
  transition: var(--el-transition-duration);
}

.toolbar {
  padding: 0 0 15px;
}
</style>
