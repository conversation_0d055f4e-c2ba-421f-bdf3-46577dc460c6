import { reactive, ref } from "vue";
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const checkedColumnLabels = ref([
    "ID",
    "节点名称",
    "节点类型",
    "IP地址",
    "状态"
  ]);
  const filterStatus = (value: any, row: { status: any }) => {
    return row.status === value;
  };

  const columns: TableColumnList = [
    /* {
      type: "selection",
      align: "left",
      width: 50
    }, */
    {
      label: "ID",
      prop: "ID",
      width: 100,
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("ID"),
      sortable: true
    },
    {
      label: "节点名称",
      prop: "nodeName",
      width: 200,
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("节点名称"),
      sortable: true
    },
    {
      label: "节点类型",
      prop: "nodeType",
      showOverflowTooltip: true,
      sortable: true,
      hide: () => !checkedColumnLabels.value.includes("节点类型")
    },
    {
      label: "IP地址",
      prop: "ipAddress",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("IP地址"),
      sortable: true
    },
    {
      label: "状态",
      prop: "status",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("状态"),
      cellRenderer(data: any) {
        if (data.status === "0") {
          return "离线";
        } else {
          return "在线";
        }
      },
      filters: [
        { text: "在线", value: "1" },
        { text: "离线", value: "0" }
      ],
      filterMethod: filterStatus,
      filterPlacement: "bottom-end"
    }
  ];

  const originalData = ref([
    {
      ID: 1,
      nodeName: "test",
      nodeType: "nvfs",
      ipAddress: "**********",
      status: "1"
    },
    {
      ID: 2,
      nodeName: "test1",
      nodeType: "nvfs",
      ipAddress: "**********",
      status: "1"
    },
    {
      ID: 3,
      nodeName: "test2",
      nodeType: "nvfs",
      ipAddress: "**********",
      status: "1"
    }
  ]);

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: originalData.value.length,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = () => {
    loading.value = true;
    setTimeout(() => {
      loading.value = false;
      message("数据刷新成功", { type: "success" });
    }, 1000);
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClearSearch
  };
}
