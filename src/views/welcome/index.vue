<script setup lang="ts">
import { useTranslationLang } from "@/layout/hooks/useTranslationLang";
import { useGlobal } from "@pureadmin/utils";
import { transformI18n } from "@/plugins/i18n";
defineOptions({
  name: "Welcome"
});

const { locale, translationCh, translationEn } = useTranslationLang();
const { $storage } = useGlobal<GlobalPropertiesApi>();
</script>

<template>
  <div class="welcome-container">
    <h1>{{ transformI18n("menus.pureHome") }} - 管理平台V3</h1>

    <!-- 国际化测试区域 -->
    <div class="i18n-test-section">
      <h2>国际化功能测试</h2>
      <p>当前语言: {{ locale }}</p>
      <p>存储的语言: {{ $storage.locale?.locale }}</p>

      <div class="button-group">
        <el-button
          @click="translationCh"
          :type="locale === 'zh' ? 'primary' : 'default'"
        >
          切换到中文
        </el-button>
        <el-button
          @click="translationEn"
          :type="locale === 'en' ? 'primary' : 'default'"
        >
          Switch to English
        </el-button>
      </div>

      <!-- 测试文本 -->
      <div class="test-texts">
        <p>登录按钮: {{ transformI18n("buttons.pureLogin") }}</p>
        <p>退出按钮: {{ transformI18n("buttons.pureLoginOut") }}</p>
        <p>加载状态: {{ transformI18n("status.pureLoad") }}</p>
        <p>用户名: {{ transformI18n("login.pureUsername") }}</p>
        <p>密码: {{ transformI18n("login.purePassword") }}</p>
      </div>
    </div>
  </div>
</template>

<style scoped>
.welcome-container {
  padding: 20px;
}

.i18n-test-section {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #f9f9f9;
}

.button-group {
  margin: 15px 0;
}

.button-group .el-button {
  margin-right: 10px;
}

.test-texts {
  margin-top: 20px;
  padding: 15px;
  background-color: white;
  border-radius: 4px;
}

.test-texts p {
  margin: 8px 0;
  padding: 5px 10px;
  background-color: #f0f0f0;
  border-radius: 4px;
}
</style>
