import { getPlatformUserList } from "@/api/permission/platform-user";
import { message } from "@/utils/message";
import type { PaginationProps } from "@pureadmin/table";
import type { PlusColumn } from "plus-pro-components";
import { onMounted, reactive, ref } from "vue";

export function useColumns() {
  const loading = ref(false);
  const searchValue = ref("");
  const multipleSelection = ref([]);
  const tableRef = ref();
  const originalData = ref([]);

  const getList = async () => {
    loading.value = true;
    const { success, data } = await getPlatformUserList();
    if (success) {
      originalData.value = data.list;
      pagination.total = originalData.value.length;
    } else {
      message("获取平台用户列表失败", { type: "error" });
      originalData.value = [];
    }
    loading.value = false;
  };

  onMounted(async () => {
    await getList();
  });

  const checkedColumnLabels = ref([
    "平台用户名",
    "角色",
    "所属集群",
    "创建时间",
    "更新时间",
    "操作"
  ]);

  const columns: TableColumnList = [
    {
      type: "selection",
      align: "left",
      width: 100
    },
    {
      label: "平台用户名",
      prop: "username",
      width: 200,
      hide: () => !checkedColumnLabels.value.includes("平台用户名")
    },
    {
      label: "角色",
      prop: "role",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("角色")
    },
    {
      label: "所属集群",
      prop: "cluster",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("所属集群")
    },
    {
      label: "创建时间",
      prop: "createTime",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("创建时间")
    },
    {
      label: "更新时间",
      prop: "updateTime",
      showOverflowTooltip: true,
      hide: () => !checkedColumnLabels.value.includes("更新时间")
    },
    {
      label: "操作",
      fixed: "right",
      slot: "operation",
      hide: () => !checkedColumnLabels.value.includes("操作")
    }
  ];

  const pagination = reactive<PaginationProps>({
    pageSize: 5,
    currentPage: 1,
    pageSizes: [5, 10, 20, 50],
    total: 0,
    align: "right",
    background: true,
    size: "default"
  });

  const handleSelectionChange = (val: any[]) => {
    multipleSelection.value = val;
  };

  const handleRefreshTable = async () => {
    await getList();
    pagination.total = originalData.value.length;
    message("刷新成功", { type: "success" });
  };

  const handleClick = (row: { username: any }) => {
    message(`查看平台用户名: ${row.username}`, {
      showClose: true,
      type: "info"
    });
  };

  const handleClearSearch = () => {
    searchValue.value = "";
  };

  const dialogFormColumns: PlusColumn[] = [
    {
      label: "平台用户名",
      width: 120,
      prop: "username",
      valueType: "input"
    },
    {
      label: "密码",
      width: 120,
      prop: "password",
      valueType: "input",
      fieldProps: {
        showPassword: true,
        placeholder: "输入密码"
      }
    },
    {
      label: "角色",
      width: 120,
      prop: "role",
      valueType: "select",
      options: [
        {
          label: "租户管理员",
          value: "tenant"
        },
        {
          label: "安全审计员",
          value: "audit"
        }
      ]
    },
    {
      label: "所属集群",
      width: 120,
      prop: "cluster",
      // 多选
      valueType: "select",
      fieldProps: {
        multiple: true
      },
      hideInForm: true,
      options: [
        {
          label: "cluster0",
          value: "cluster0"
        },
        {
          label: "cluster1",
          value: "cluster1"
        },
        {
          label: "cluster2",
          value: "cluster2"
        }
      ]
    }
  ];

  const dialogFormRules = {
    username: [
      {
        required: true,
        message: "名称请以字母开头，后面跟着字母、数字的组合，长度最大为15位",
        pattern: /^[a-zA-Z][a-zA-Z0-9]{0,14}$/,
        trigger: "change"
      }
    ],
    password: [
      {
        required: true,
        message: "只能包含大小写字母，数字，以及特殊字符!@#$%?,长度应为3到32位",
        pattern: /^[a-zA-Z0-9!@#$%?,]{3,32}$/,
        trigger: "change"
      }
    ],
    role: [
      {
        required: true,
        message: "请选择角色",
        trigger: "change"
      }
    ]
  };

  return {
    loading,
    searchValue,
    multipleSelection,
    tableRef,
    columns,
    originalData,
    pagination,
    checkedColumnLabels,
    handleSelectionChange,
    handleRefreshTable,
    handleClick,
    handleClearSearch,
    dialogFormColumns,
    dialogFormRules
  };
}
