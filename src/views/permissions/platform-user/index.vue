<script setup lang="ts">
import { ColumnSetting } from "@/components/ColumnSetting";
import InfoAlert from "@/components/InfoAlert";
import PageHeader from "@/components/PageHeader";
import IcOutline<PERSON>ock<PERSON>erson from "~icons/ic/outline-lock-person";
import IcOutlineSearch from "~icons/ic/outline-search";
import IcOutlineRefresh from "~icons/ic/outline-refresh";
import { useColumns } from "./columns";
import { TableToolbar } from "@/components/TableToolbar";
import { type FieldValues, PlusDialogForm } from "plus-pro-components";
import { message } from "@/utils/message";
import { h, ref } from "vue";
import IcOutlineDelete from "~icons/ic/outline-delete";
import IcOutlineEdit from "~icons/ic/outline-edit";
import { addDialog, closeDialog } from "@/components/ReDialog";

defineOptions({
  name: "PlatformUser"
});
const {
  loading,
  searchValue,
  multipleSelection,
  tableRef,
  columns,
  originalData,
  checkedColumnLabels,
  handleSelectionChange,
  handleRefreshTable,
  handleClick,
  handleClearSearch,
  pagination,
  dialogFormColumns,
  dialogFormRules
} = useColumns();

const dialogVisible = ref(false);
const dialogFormValues = ref<FieldValues>({});
const confirmLoading = ref(false);

const handleOpen = () => {
  dialogVisible.value = true;
};

const handleSubmit = async () => {
  console.log(dialogFormValues.value);
  message("新增成功", {
    type: "success"
  });
};

const handleCancel = () => {
  dialogVisible.value = false;
  dialogFormValues.value = {};
  confirmLoading.value = false;
};

const handleEditOpen = (row: any) => {
  dialogFormValues.value = { ...row };
  dialogVisible.value = true;
  confirmLoading.value = false;
  dialogFormColumns.forEach((item: any) => {
    if (item.prop === "cluster") {
      item.hideInForm = false;
    }
  });
};

function onCloseCallBackClick(multipleSelection: any) {
  addDialog({
    title: "删除平台用户",
    width: 600,
    contentRenderer: () =>
      h(
        "p",
        multipleSelection.length
          ? `确定删除选中${multipleSelection.length}个平台用户吗？删除后无法恢复，请谨慎操作`
          : "确定删除选中平台用户吗？删除后无法恢复，请谨慎操作"
      ),
    footerButtons: [
      {
        label: "取消",
        btnClick: ({ dialog: { options, index }, button }) => {
          console.log("取消删除");
          closeDialog(options, index);
        }
      },
      {
        label: "确认",
        type: "danger",
        btnClick: ({ dialog: { options, index }, button }) => {
          console.log("确认删除");
          closeDialog(options, index);
        }
      }
    ]
  });
}
</script>

<template>
  <div class="storage-pool-container">
    <el-card class="mb-4 shadow-sm">
      <template #header>
        <PageHeader
          :icon="IcOutlineLockPerson"
          title="平台用户管理"
          subtitle="Platform User Management"
        />
      </template>
      <InfoAlert
        description="1. 平台用户管理负责平台用户及权限的增删改查。<br />2. 并且可以给平台用户设定平台的使用权限，以及查看权限。"
      />
    </el-card>
    <el-card class="shadow-sm">
      <template #header>
        <TableToolbar :multiple-selection="multipleSelection">
          <template #search>
            <el-input
              v-model="searchValue"
              placeholder="按关键词搜索"
              style="width: 250px"
              clearable
              @clear="handleClearSearch"
            >
              <template #prefix>
                <el-icon>
                  <IcOutlineSearch />
                </el-icon>
              </template>
            </el-input>
          </template>
          <template #actions>
            <el-button
              type="default"
              :icon="IcOutlineRefresh"
              :loading="loading"
              @click="handleRefreshTable"
            >
              刷新
            </el-button>
            <el-button type="primary" @click="handleOpen"> 新增 </el-button>
            <el-button
              type="danger"
              :disabled="multipleSelection.length === 0"
              @click="onCloseCallBackClick(multipleSelection)"
            >
              删除
            </el-button>
          </template>
          <template #settings>
            <ColumnSetting
              v-model:checkedColumnLabels="checkedColumnLabels"
              :columns="columns"
            />
          </template>
        </TableToolbar>
      </template>
      <pure-table
        ref="tableRef"
        :data="originalData"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        row-key="ID"
        border
        @selection-change="handleSelectionChange"
      >
        <template #operation="{ row }">
          <el-button
            type="primary"
            plain
            size="small"
            :icon="IcOutlineEdit"
            @click="handleEditOpen(row)"
          />
          <el-button
            type="danger"
            plain
            size="small"
            :icon="IcOutlineDelete"
            @click="onCloseCallBackClick"
          />
        </template>
      </pure-table>
    </el-card>
    <PlusDialogForm
      v-model:visible="dialogVisible"
      v-model="dialogFormValues"
      :dialog="{
        title: '新增平台用户',
        width: 600,
        closeOnPressEscape: true,
        destroyOnClose: true,
        confirmLoading
      }"
      :form="{
        columns: dialogFormColumns,
        rules: dialogFormRules,
        labelPosition: 'left',
        labelWidth: '150px',
        clearable: true
      }"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    />
  </div>
</template>
