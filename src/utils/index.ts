// 字节转换 KB/MB/GB/TB
export function bytesToSize(bytes: number) {
  if (Number(bytes) === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

export const getNames = (multipleSelection: any, type: any) => {
  if (!multipleSelection.length) return "";
  const names = multipleSelection.map(
    (node: { [x: string]: any }) => node[type]
  );
  if (names.length <= 3) {
    return names.join("、");
  } else {
    return `${names.slice(0, 3).join("、")}等${names.length}个`;
  }
};
