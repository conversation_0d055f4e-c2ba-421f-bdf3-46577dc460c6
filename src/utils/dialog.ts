import { h, defineComponent } from "vue";
import { ElResult } from "element-plus";
import { addDialog, type DialogOptions } from "@/components/ReDialog";

// 通用的弹窗内容组件
const ResultContent = defineComponent({
  name: "ResultContent",
  props: {
    status: { type: String, required: true },
    title: { type: String, required: true },
    description: { type: String, required: true },
    icon: { type: String, default: "success" }
  },
  setup(props) {
    return () =>
      h(ElResult, {
        status: props.status,
        title: props.title,
        subTitle: props.description,
        icon: props.icon as "success" | "error" | "warning" | "info"
      });
  }
});

/**
 * @description 弹出成功提示弹窗
 * @param title 弹窗标题，如：“成功弹窗”
 * @param description 描述信息，如：“文件子树删除成功”
 * @returns DialogOptions 返回被添加的弹窗配置对象，以便后续关闭
 */
export const successDialog = (
  title: string,
  description: string
): DialogOptions => {
  const options: DialogOptions = {
    title: "",
    hideFooter: true,
    width: "450px",
    contentRenderer: () =>
      h(ResultContent, {
        status: "success",
        title: title,
        description: description,
        icon: "success"
      })
  };
  addDialog(options);
  return options;
};

/**
 * @description 弹出失败提示弹窗
 * @param title 弹窗标题，如：“删除弹窗”
 * @param description 描述信息，如：“文件子树删除失败：xxxx”
 * @returns DialogOptions 返回被添加的弹窗配置对象，以便后续关闭
 */
export const errorDialog = (
  title: string,
  description: string
): DialogOptions => {
  const options: DialogOptions = {
    title: "",
    hideFooter: true,
    width: "450px",
    contentRenderer: () =>
      h(ResultContent, {
        status: "error",
        title: title,
        description: description,
        icon: "error"
      })
  };
  addDialog(options);
  return options;
};
