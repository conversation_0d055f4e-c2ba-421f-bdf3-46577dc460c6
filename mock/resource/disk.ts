import { randomUUID } from "crypto";
import { defineFakeRoute } from "vite-plugin-fake-server/client";

const list = [
  {
    ID: randomUUID(),
    diskName: "n3_p2_d0_aUU6OidM",
    diskSize: "1000GB",
    // 故障域
    faultDomain: "32010",
    // 碎片百分比
    fragmentPercent: "15",
    // 可用百分比
    availablePercent: "90"
  },
  {
    ID: randomUUID(),
    diskName: "n3_p2_d1_aUU6OidN",
    diskSize: "2000GB",
    faultDomain: "32011",
    fragmentPercent: "30",
    availablePercent: "57"
  },
  {
    ID: randomUUID(),
    diskName: "n3_p2_d2_aUU6OidO",
    diskSize: "1500GB",
    faultDomain: "32012",
    fragmentPercent: "60",
    availablePercent: "23"
  },
  {
    ID: randomUUID(),
    diskName: "n3_p2_d2_aUU6OidO",
    diskSize: "1500GB",
    faultDomain: "32012",
    fragmentPercent: "90",
    availablePercent: "10"
  }
];
export default defineFakeRoute([
  {
    url: "/resource/disk",
    method: "get",
    response: async ({}) => {
      await new Promise(res => setTimeout(res, 800));
      return {
        success: true,
        data: {
          list: list,
          total: list.length
        }
      };
    }
  }
]);
