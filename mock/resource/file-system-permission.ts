import { randomUUID } from "crypto";
import { defineFakeRoute } from "vite-plugin-fake-server/client";

const list = [
  {
    ID: randomUUID(),
    pathName: "test1",
    createTime: "2025-08-01 12:00:00",
    remark: "123"
  },
  {
    ID: randomUUID(),
    pathName: "test2",
    createTime: "2025-08-02 12:00:00",
    remark: "123"
  },
  {
    ID: randomUUID(),
    pathName: "test3",
    createTime: "2025-08-03 12:00:00",
    remark: "123"
  },
  {
    ID: randomUUID(),
    pathName: "test4",
    createTime: "2025-08-04 12:00:00",
    remark: "123"
  }
];
export default defineFakeRoute([
  {
    url: "/resource/file-system-permission",
    method: "get",
    response: async ({}) => {
      await new Promise(res => setTimeout(res, 500));
      return {
        success: true,
        data: {
          list: list,
          total: list.length
        }
      };
    }
  }
]);
