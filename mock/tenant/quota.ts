import { randomUUID } from "crypto";
import { defineFakeRoute } from "vite-plugin-fake-server/client";

const virtualList = [
  {
    ID: randomUUID(),
    // 模板ID
    templateID: "quota1",
    // 模板名称
    templateName: "gt-50G",
    // 模版类型
    templateType: "租户租模板",
    // 文件子树
    fileSubtree: "fs0",
    // 被使用
    used: "usergroup01",
    // 创建时间
    createTime: "2025-08-06 10:00:00",
    // 修改时间
    updateTime: "2025-08-06 10:00:00",
    // 配额
    quota: "50G",
    quotaValue: "", // 为配额数值创建字段
    quotaUnit: "GiB" // 为配额单位创建字段并设置默认值
  },
  {
    ID: randomUUID(),
    templateID: "quota2",
    templateName: "gt-100G",
    templateType: "租户模板",
    fileSubtree: "fs0",
    used: "user01",
    createTime: "2025-08-06 10:00:00",
    updateTime: "2025-08-06 10:00:00",
    quota: "20G"
  }
];
export default defineFakeRoute([
  {
    url: "/tenant/quota",
    method: "get",
    response: async ({}) => {
      await new Promise(res => setTimeout(res, 800));
      return {
        success: true,
        data: {
          list: virtualList,
          total: virtualList.length
        }
      };
    }
  }
]);
