import { randomUUID } from "crypto";
import { defineFakeRoute } from "vite-plugin-fake-server/client";

const list = [
  {
    ID: randomUUID(),
    username: "admin",
    role: "超级管理员",
    cluster: ["cluster0", "cluster1"],
    createTime: "2026-03-28 00:00:00",
    updateTime: "2026-03-28 00:00:00"
  },
  {
    ID: randomUUID(),
    username: "tenant",
    role: "租户管理员",
    cluster: ["cluster1"],
    createTime: "2026-03-28 00:00:00",
    updateTime: "2026-03-28 00:00:00"
  },
  {
    ID: randomUUID(),
    username: "audit",
    role: "安全审计员",
    cluster: ["cluster0"],
    createTime: "2026-03-28 00:00:00",
    updateTime: "2026-03-28 00:00:00"
  }
];
export default defineFakeRoute([
  {
    url: "/permission/platform-user",
    method: "get",
    response: async ({}) => {
      await new Promise(res => setTimeout(res, 500));
      return {
        success: true,
        data: {
          list: list,
          total: list.length
        }
      };
    }
  }
]);
