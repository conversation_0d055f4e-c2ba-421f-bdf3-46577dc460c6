import { randomUUID } from "crypto";
import { defineFakeRoute } from "vite-plugin-fake-server/client";

const virtualList = [
  {
    ID: randomUUID(),
    groupName: "1750060178-20854.mclient01",
    nodeName: "mclient01",
    nodeIp: "**********",
    status: "1",
    tenant: "root",
    createTime: "2025-08-06 10:00:00"
  },
  {
    ID: randomUUID(),
    groupName: "1750060178-20854.mclient02",
    nodeName: "mclient02",
    nodeIp: "**********",
    status: "0",
    tenant: "root",
    createTime: "2025-08-06 10:02:00"
  },
  {
    ID: randomUUID(),
    groupName: "1750060178-20854.mclient03",
    nodeName: "mclient03",
    nodeIp: "**********",
    status: "1",
    tenant: "root",
    createTime: "2025-08-06 10:03:00"
  },
  {
    ID: randomUUID(),
    groupName: "1750060178-20854.mclient04",
    nodeName: "mclient04",
    nodeIp: "**********",
    status: "0",
    tenant: "root",
    createTime: "2025-08-06 10:04:00"
  }
];
export default defineFakeRoute([
  {
    url: "/cluster/node/list",
    method: "get",
    response: async ({}) => {
      await new Promise(res => setTimeout(res, 800));
      return {
        success: true,
        data: {
          list: virtualList,
          total: virtualList.length
        }
      };
    }
  }
]);
